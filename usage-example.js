// 页面中使用批量操作模态框组件的示例代码

Page({
  data: {
    // 批量操作相关
    showBatchModal: false,
    batchOperation: 'copy', // 'copy' 或 'import'
    
    // 服务实例（需要在页面初始化时设置）
    timeSegmentService: null,
    holidayManager: null,
    currentWorkId: '',
    currentDate: '',
  },

  onLoad() {
    // 初始化服务实例
    this.timeSegmentService = getApp().getTimeSegmentService()
    this.holidayManager = getApp().getHolidayManager()
    
    // 获取当前工作ID
    const currentWork = getApp().getDataManager().getCurrentWork()
    const currentWorkId = currentWork ? currentWork.id : ''
    
    // 获取当前日期
    const now = new Date()
    const currentDate = this.formatDate(now)

    // 获取任职日期范围
    const employmentRange = this.getEmploymentDateRange()

    this.setData({
      timeSegmentService: this.timeSegmentService,
      holidayManager: this.holidayManager,
      currentWorkId,
      currentDate,
      employmentStartDate: employmentRange.startDate,
      employmentEndDate: employmentRange.endDate
    })
  },

  /**
   * 获取任职日期范围
   */
  getEmploymentDateRange() {
    const currentWork = getApp().getDataManager().getCurrentWork()
    if (!currentWork) {
      return {
        startDate: '',
        endDate: ''
      }
    }

    return {
      startDate: currentWork.startDate || '',
      endDate: currentWork.endDate || ''
    }
  },

  /**
   * 格式化日期
   */
  formatDate(date) {
    const year = date.getFullYear()
    const month = String(date.getMonth() + 1).padStart(2, '0')
    const day = String(date.getDate()).padStart(2, '0')
    return `${year}-${month}-${day}`
  },

  /**
   * 打开批量复制模态框
   */
  onBatchCopy() {
    this.setData({
      showBatchModal: true,
      batchOperation: 'copy'
    })
  },

  /**
   * 打开导入模态框
   */
  onImportSchedule() {
    this.setData({
      showBatchModal: true,
      batchOperation: 'import'
    })
  },

  /**
   * 关闭批量操作模态框
   */
  onCloseBatchModal() {
    this.setData({
      showBatchModal: false
    })
  },

  /**
   * 取消批量操作
   */
  onCancelBatchOperation() {
    this.setData({
      showBatchModal: false
    })
  },

  /**
   * 批量操作数据更新回调
   */
  onBatchDataUpdated(e) {
    const { operation, sourceDate, targetDates, count } = e.detail
    
    console.log('批量操作完成:', {
      operation,
      sourceDate,
      targetDates,
      count
    })
    
    // 刷新页面数据
    this.refreshPageData()
    
    // 显示成功提示
    if (operation === 'copy') {
      wx.showToast({
        title: `成功复制到${count}个日期`,
        icon: 'success'
      })
    } else if (operation === 'import') {
      wx.showToast({
        title: '导入成功',
        icon: 'success'
      })
    }
  },

  /**
   * 刷新页面数据
   */
  refreshPageData() {
    // 这里实现你的页面数据刷新逻辑
    // 例如重新生成日历、更新统计信息等
    console.log('刷新页面数据...')
  }
})
