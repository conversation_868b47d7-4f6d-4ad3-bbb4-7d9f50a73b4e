<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>批量操作模态框 - 样式重构预览</title>
    <style>
        /* 引入重构后的样式变量 */
        :root {
            --primary-color: #007aff;
            --primary-hover: #0056b3;
            --success-color: #34c759;
            --warning-color: #ff9500;
            --danger-color: #ff3b30;
            --text-primary: #1d1d1f;
            --text-secondary: #86868b;
            --background-primary: #ffffff;
            --background-secondary: #f2f2f7;
            --border-color: #d1d1d6;
            --shadow-light: 0 2px 10px rgba(0, 0, 0, 0.1);
            --shadow-medium: 0 4px 20px rgba(0, 0, 0, 0.15);
            --radius-small: 8px;
            --radius-medium: 12px;
            --radius-large: 16px;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', <PERSON><PERSON>, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            min-height: 100vh;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
        }

        .header {
            text-align: center;
            margin-bottom: 40px;
        }

        .title {
            font-size: 32px;
            font-weight: 700;
            color: var(--text-primary);
            margin-bottom: 12px;
        }

        .subtitle {
            font-size: 18px;
            color: var(--text-secondary);
            font-weight: 500;
        }

        .preview-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 24px;
            margin-bottom: 40px;
        }

        .preview-card {
            background: var(--background-primary);
            border-radius: var(--radius-medium);
            padding: 24px;
            box-shadow: var(--shadow-medium);
            border: 1px solid var(--border-color);
        }

        .card-title {
            font-size: 20px;
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: 16px;
        }

        /* 模拟日历样式 */
        .calendar-demo {
            display: grid;
            grid-template-columns: repeat(7, 1fr);
            gap: 8px;
            margin-bottom: 16px;
        }

        .calendar-day {
            aspect-ratio: 1;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: var(--radius-small);
            font-weight: 600;
            cursor: pointer;
            transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
            background: var(--background-primary);
            border: 1px solid var(--border-color);
        }

        .calendar-day:active {
            transform: scale(0.95);
        }

        .calendar-day.has-data {
            background: linear-gradient(135deg, #dbeafe 0%, #bfdbfe 100%);
            border-color: var(--primary-color);
        }

        .calendar-day.selected-source {
            background: linear-gradient(135deg, var(--warning-color) 0%, #ff8c00 100%);
            color: white;
            transform: scale(1.05);
            box-shadow: 0 4px 12px rgba(255, 149, 0, 0.4);
        }

        .calendar-day.selected-target {
            background: linear-gradient(135deg, var(--success-color) 0%, #28a745 100%);
            color: white;
            transform: scale(1.05);
            box-shadow: 0 4px 12px rgba(52, 199, 89, 0.4);
        }

        .calendar-day.is-today {
            border: 2px solid var(--danger-color);
            background: linear-gradient(135deg, #fef2f2 0%, #fee2e2 100%);
            color: var(--danger-color);
        }

        /* 按钮样式 */
        .btn-demo {
            display: flex;
            gap: 12px;
            flex-wrap: wrap;
        }

        .btn {
            padding: 12px 20px;
            border-radius: var(--radius-small);
            font-weight: 600;
            cursor: pointer;
            transition: all 0.2s ease;
            border: none;
            font-size: 14px;
        }

        .btn-primary {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-hover) 100%);
            color: white;
            box-shadow: 0 2px 8px rgba(0, 122, 255, 0.3);
        }

        .btn-primary:active {
            transform: scale(0.98);
        }

        .btn-secondary {
            background: var(--background-secondary);
            color: var(--text-secondary);
            border: 1px solid var(--border-color);
        }

        .btn-secondary:active {
            background: var(--border-color);
        }

        /* 卡片样式 */
        .info-card {
            background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
            border: 1px solid #bae6fd;
            border-radius: var(--radius-medium);
            padding: 16px;
            margin-bottom: 16px;
        }

        .warning-card {
            background: linear-gradient(135deg, #fef2f2 0%, #fee2e2 100%);
            border: 1px solid #fca5a5;
            border-radius: var(--radius-medium);
            padding: 16px;
            margin-bottom: 16px;
        }

        .success-card {
            background: linear-gradient(135deg, #f0fdf4 0%, #dcfce7 100%);
            border: 1px solid #bbf7d0;
            border-radius: var(--radius-medium);
            padding: 16px;
            margin-bottom: 16px;
        }

        .features {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-top: 40px;
        }

        .feature {
            text-align: center;
            padding: 20px;
        }

        .feature-icon {
            font-size: 48px;
            margin-bottom: 16px;
        }

        .feature-title {
            font-size: 18px;
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: 8px;
        }

        .feature-desc {
            color: var(--text-secondary);
            line-height: 1.5;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1 class="title">🎨 批量操作模态框样式重构</h1>
            <p class="subtitle">现代化设计 · 流畅交互 · 优雅体验</p>
        </div>

        <div class="preview-grid">
            <div class="preview-card">
                <h3 class="card-title">📅 日历组件预览</h3>
                <div class="calendar-demo">
                    <div class="calendar-day">1</div>
                    <div class="calendar-day has-data">2</div>
                    <div class="calendar-day">3</div>
                    <div class="calendar-day selected-source">4</div>
                    <div class="calendar-day">5</div>
                    <div class="calendar-day selected-target">6</div>
                    <div class="calendar-day">7</div>
                    <div class="calendar-day">8</div>
                    <div class="calendar-day has-data">9</div>
                    <div class="calendar-day">10</div>
                    <div class="calendar-day">11</div>
                    <div class="calendar-day">12</div>
                    <div class="calendar-day">13</div>
                    <div class="calendar-day is-today">14</div>
                </div>
                <p style="font-size: 13px; color: var(--text-secondary);">
                    🟦 有数据 🟠 源日期 🟢 目标日期 🔴 今天
                </p>
            </div>

            <div class="preview-card">
                <h3 class="card-title">🎛️ 按钮组件</h3>
                <div class="btn-demo">
                    <button class="btn btn-primary">确定复制</button>
                    <button class="btn btn-secondary">取消</button>
                    <button class="btn btn-primary">下一步</button>
                </div>
            </div>

            <div class="preview-card">
                <h3 class="card-title">💡 信息卡片</h3>
                <div class="info-card">
                    <strong>💡 提示</strong><br>
                    选择要复制的源日期（需要有工作安排）
                </div>
                <div class="warning-card">
                    <strong>⚠️ 数据覆盖提示</strong><br>
                    选中的日期中有 3 个日期已有数据
                </div>
                <div class="success-card">
                    <strong>✅ 选择统计</strong><br>
                    已选择 5 个日期，本月工作日 3/22 个
                </div>
            </div>
        </div>

        <div class="features">
            <div class="feature">
                <div class="feature-icon">🎨</div>
                <h3 class="feature-title">现代化设计</h3>
                <p class="feature-desc">采用最新的设计语言，渐变背景、圆角设计、阴影层次</p>
            </div>
            <div class="feature">
                <div class="feature-icon">⚡</div>
                <h3 class="feature-title">流畅动画</h3>
                <p class="feature-desc">平滑的过渡动画，触觉反馈，提升操作体验</p>
            </div>
            <div class="feature">
                <div class="feature-icon">🎯</div>
                <h3 class="feature-title">清晰层次</h3>
                <p class="feature-desc">明确的视觉层次，直观的状态反馈，易于理解</p>
            </div>
            <div class="feature">
                <div class="feature-icon">📱</div>
                <h3 class="feature-title">响应式设计</h3>
                <p class="feature-desc">适配不同屏幕尺寸，保持一致的用户体验</p>
            </div>
        </div>
    </div>
</body>
</html>
