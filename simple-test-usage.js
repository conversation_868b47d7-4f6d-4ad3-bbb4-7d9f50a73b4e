// 简化的测试用法 - 用于调试组件问题

Page({
  data: {
    // 批量操作相关
    showBatchModal: false,
    batchOperation: 'copy',
    
    // 基本数据
    currentWorkId: 'test-work-id',
    currentDate: '2024-01-15',
    employmentStartDate: '2024-01-01',
    employmentEndDate: '2024-12-31',
    
    // 服务实例 - 先设为null，让组件使用全局服务
    timeSegmentService: null,
    holidayManager: null
  },

  onLoad() {
    console.log('测试页面加载完成')
  },

  /**
   * 打开批量复制模态框
   */
  onBatchCopy() {
    console.log('打开批量复制模态框')
    this.setData({
      showBatchModal: true,
      batchOperation: 'copy'
    })
  },

  /**
   * 关闭批量操作模态框
   */
  onCloseBatchModal() {
    console.log('关闭批量操作模态框')
    this.setData({
      showBatchModal: false
    })
  },

  /**
   * 取消批量操作
   */
  onCancelBatchOperation() {
    console.log('取消批量操作')
    this.setData({
      showBatchModal: false
    })
  },

  /**
   * 批量操作数据更新回调
   */
  onBatchDataUpdated(e) {
    console.log('批量操作数据更新:', e.detail)
    
    wx.showToast({
      title: '操作成功',
      icon: 'success'
    })
  }
})
