/**
 * 通用API模块
 * 封装一些通用的API调用，避免直接使用 api.call
 */

import baseApiClient from '../base.js'
import enhancedApiClient from '../enhanced.js'

/**
 * 通用API类
 */
export class GeneralApi {
  constructor() {
    this.baseClient = baseApiClient
    this.enhancedClient = enhancedApiClient
    
    // 缓存配置
    this.cacheConfig = {
      // 获取更新日志的缓存配置
      getChangelogList: {
        ttl: 30 * 60 * 1000,        // 缓存30分钟
        ignoreKeys: []
      },
      
      // 获取摸鱼人数的缓存配置
      getCurrentFishingCount: {
        ttl: 30 * 1000,             // 缓存30秒
        ignoreKeys: []
      },
      
      // 兑换码相关不缓存（涉及状态变更）
      redeemCode: {
        ttl: 0,                     // 不缓存
        ignoreKeys: []
      }
    }
  }

  /**
   * 获取更新日志列表
   * @param {Object} params - 查询参数
   * @param {Object} options - 调用选项
   * @returns {Promise<Object>} 更新日志列表
   */
  async getChangelogList(params = {}, options = {}) {
    const finalOptions = {
      cache: true,
      ttl: this.cacheConfig.getChangelogList.ttl,
      showLoading: true,
      showError: true,
      ...options
    }

    return this.enhancedClient.callWithOptions('getChangelogList', params, finalOptions)
  }

  /**
   * 提交反馈
   * @param {Object} params - 反馈数据
   * @param {Object} options - 调用选项
   * @returns {Promise<Object>} 提交结果
   */
  async submitFeedback(params = {}, options = {}) {
    const finalOptions = {
      cache: false,               // 提交操作不缓存
      showLoading: true,
      showError: true,
      retry: true,                // 提交失败时重试
      retryConfig: {
        maxRetries: 2,
        baseDelay: 1000
      },
      ...options
    }

    return this.enhancedClient.callWithOptions('submitFeedback', params, finalOptions)
  }

  /**
   * 获取当前摸鱼人数
   * @param {Object} options - 调用选项
   * @returns {Promise<Object>} 摸鱼人数
   */
  async getCurrentFishingCount(options = {}) {
    const finalOptions = {
      cache: true,
      ttl: this.cacheConfig.getCurrentFishingCount.ttl,
      showLoading: false,         // 通常在仪表盘中调用，不显示loading
      showError: false,           // 失败时不显示错误，由调用方处理
      ...options
    }

    return this.enhancedClient.callWithOptions('getCurrentFishingCount', {}, finalOptions)
  }

  /**
   * 兑换码兑换
   * @param {Object} params - 兑换参数
   * @param {Object} options - 调用选项
   * @returns {Promise<Object>} 兑换结果
   */
  async redeemCode(params = {}, options = {}) {
    const finalOptions = {
      cache: false,               // 兑换操作不缓存
      showLoading: true,
      showError: false,           // 由组件自己处理错误显示
      retry: true,                // 兑换失败时重试
      retryConfig: {
        maxRetries: 2,
        baseDelay: 1000
      },
      ...options
    }

    return this.enhancedClient.callWithOptions('redeemCode', params, finalOptions)
  }

  /**
   * 清除通用API缓存
   * @param {string} type - 缓存类型 ('all' | 'changelog' | 'fishing')
   */
  clearGeneralCache(type = 'all') {
    const cacheKeys = []
    
    switch (type) {
      case 'changelog':
        cacheKeys.push('getChangelogList')
        break
      case 'fishing':
        cacheKeys.push('getCurrentFishingCount')
        break
      case 'all':
        cacheKeys.push('getChangelogList', 'getCurrentFishingCount')
        break
      default:
        console.warn(`[GeneralApi] 未知的缓存类型: ${type}`)
        return
    }

    cacheKeys.forEach(key => {
      this.enhancedClient.cache.clearByPattern(key)
    })
    
    console.log(`[GeneralApi] 已清除缓存: ${cacheKeys.join(', ')}`)
  }

  /**
   * 预热通用API缓存
   */
  async warmupCache() {
    console.log('[GeneralApi] 开始预热缓存')
    
    try {
      // 并行预热常用API
      await Promise.all([
        this.getCurrentFishingCount({ cache: true }),
        this.getChangelogList({ page: 1, pageSize: 10 }, { cache: true })
      ])
      
      console.log('[GeneralApi] 缓存预热完成')
    } catch (error) {
      console.error('[GeneralApi] 缓存预热失败:', error)
    }
  }

  /**
   * 获取通用API统计信息
   */
  getStats() {
    return {
      cacheConfig: this.cacheConfig,
      supportedApis: [
        'getChangelogList',
        'submitFeedback', 
        'getCurrentFishingCount',
        'redeemCode'
      ]
    }
  }
}

// 创建单例实例
const generalApi = new GeneralApi()

export default generalApi
