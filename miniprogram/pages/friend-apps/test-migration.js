/**
 * 友情应用页面迁移测试
 * 用于验证新API系统的功能
 */

import { api } from '../../core/api/index.js'

/**
 * 测试新API系统的各种功能
 */
export const testMigration = {
  
  /**
   * 测试基础API调用
   */
  async testBasicCall() {
    console.log('=== 测试基础API调用 ===')
    
    try {
      const result = await api.call('getFriendApps')
      console.log('✅ 基础调用成功:', result)
      return result
    } catch (error) {
      console.error('❌ 基础调用失败:', error)
      throw error
    }
  },

  /**
   * 测试模块化API调用
   */
  async testModuleCall() {
    console.log('=== 测试模块化API调用 ===')
    
    try {
      const result = await api.friendApps.getFriendApps()
      console.log('✅ 模块化调用成功:', result)
      return result
    } catch (error) {
      console.error('❌ 模块化调用失败:', error)
      throw error
    }
  },

  /**
   * 测试缓存功能
   */
  async testCacheFeature() {
    console.log('=== 测试缓存功能 ===')
    
    try {
      // 第一次调用（应该从网络获取）
      console.log('第一次调用（网络请求）...')
      const start1 = Date.now()
      const result1 = await api.friendApps.getFriendAppsWithCache()
      const time1 = Date.now() - start1
      console.log(`✅ 第一次调用成功，耗时: ${time1}ms`)

      // 第二次调用（应该从缓存获取）
      console.log('第二次调用（缓存获取）...')
      const start2 = Date.now()
      const result2 = await api.friendApps.getFriendAppsWithCache()
      const time2 = Date.now() - start2
      console.log(`✅ 第二次调用成功，耗时: ${time2}ms`)

      // 比较耗时
      if (time2 < time1) {
        console.log(`🚀 缓存生效！速度提升: ${((time1 - time2) / time1 * 100).toFixed(1)}%`)
      }

      return { result1, result2, time1, time2 }
    } catch (error) {
      console.error('❌ 缓存测试失败:', error)
      throw error
    }
  },

  /**
   * 测试不同参数的差异化缓存
   */
  async testDifferentParamsCache() {
    console.log('=== 测试差异化缓存 ===')
    
    try {
      // 测试不同分类的缓存
      console.log('测试分类缓存...')
      const gameApps = await api.friendApps.getFriendAppsByCategory('game')
      const toolApps = await api.friendApps.getFriendAppsByCategory('tool')
      console.log('✅ 分类缓存测试成功')

      // 测试不同排序的缓存
      console.log('测试排序缓存...')
      const popularApps = await api.friendApps.getFriendAppsBySort('popular')
      const latestApps = await api.friendApps.getFriendAppsBySort('latest')
      console.log('✅ 排序缓存测试成功')

      // 测试搜索缓存
      console.log('测试搜索缓存...')
      const searchResult1 = await api.friendApps.searchFriendApps('小程序')
      const searchResult2 = await api.friendApps.searchFriendApps('游戏')
      console.log('✅ 搜索缓存测试成功')

      return {
        gameApps,
        toolApps,
        popularApps,
        latestApps,
        searchResult1,
        searchResult2
      }
    } catch (error) {
      console.error('❌ 差异化缓存测试失败:', error)
      throw error
    }
  },

  /**
   * 测试缓存管理功能
   */
  async testCacheManagement() {
    console.log('=== 测试缓存管理 ===')
    
    try {
      // 获取缓存统计
      const statsBefore = api.getStats()
      console.log('缓存统计（清理前）:', statsBefore)

      // 清除特定缓存
      const deletedCount = api.friendApps.clearFriendAppsCache('category', 'game')
      console.log(`✅ 清除分类缓存成功，删除 ${deletedCount} 项`)

      // 清除所有友情应用缓存
      api.friendApps.clearFriendAppsCache('all')
      console.log('✅ 清除所有友情应用缓存成功')

      // 获取清理后的统计
      const statsAfter = api.getStats()
      console.log('缓存统计（清理后）:', statsAfter)

      return { statsBefore, statsAfter, deletedCount }
    } catch (error) {
      console.error('❌ 缓存管理测试失败:', error)
      throw error
    }
  },

  /**
   * 测试预热缓存功能
   */
  async testCacheWarmup() {
    console.log('=== 测试缓存预热 ===')
    
    try {
      // 预热缓存
      await api.friendApps.warmupCache()
      console.log('✅ 缓存预热成功')

      // 检查预热后的统计
      const stats = api.getStats()
      console.log('预热后缓存统计:', stats)

      return stats
    } catch (error) {
      console.error('❌ 缓存预热测试失败:', error)
      throw error
    }
  },

  /**
   * 测试重试功能
   */
  async testRetryFeature() {
    console.log('=== 测试重试功能 ===')
    
    try {
      // 使用重试功能调用API
      const result = await api.callWithOptions('getFriendApps', {}, {
        retry: true,
        retryConfig: {
          maxRetries: 2,
          baseDelay: 500
        },
        showLoading: false,
        showError: false
      })
      
      console.log('✅ 重试功能测试成功:', result)
      return result
    } catch (error) {
      console.error('❌ 重试功能测试失败:', error)
      throw error
    }
  },

  /**
   * 运行所有测试
   */
  async runAllTests() {
    console.log('🚀 开始运行友情应用页面迁移测试...')
    
    const results = {}
    
    try {
      // 基础功能测试
      results.basicCall = await this.testBasicCall()
      results.moduleCall = await this.testModuleCall()
      
      // 缓存功能测试
      results.cacheFeature = await this.testCacheFeature()
      results.differentParamsCache = await this.testDifferentParamsCache()
      results.cacheManagement = await this.testCacheManagement()
      results.cacheWarmup = await this.testCacheWarmup()
      
      // 高级功能测试
      results.retryFeature = await this.testRetryFeature()
      
      console.log('🎉 所有测试完成！')
      console.log('测试结果汇总:', results)
      
      return results
    } catch (error) {
      console.error('💥 测试过程中出现错误:', error)
      throw error
    }
  },

  /**
   * 性能对比测试
   */
  async performanceComparison() {
    console.log('=== 性能对比测试 ===')
    
    try {
      // 清除所有缓存
      api.clearCache()
      
      // 测试多次调用的性能
      const iterations = 5
      const times = []
      
      for (let i = 0; i < iterations; i++) {
        const start = Date.now()
        await api.friendApps.getFriendAppsWithCache()
        const time = Date.now() - start
        times.push(time)
        console.log(`第 ${i + 1} 次调用耗时: ${time}ms`)
      }
      
      const avgTime = times.reduce((a, b) => a + b, 0) / times.length
      const firstCallTime = times[0]
      const cachedCallsAvgTime = times.slice(1).reduce((a, b) => a + b, 0) / (times.length - 1)
      
      console.log(`首次调用耗时: ${firstCallTime}ms`)
      console.log(`缓存调用平均耗时: ${cachedCallsAvgTime.toFixed(1)}ms`)
      console.log(`性能提升: ${((firstCallTime - cachedCallsAvgTime) / firstCallTime * 100).toFixed(1)}%`)
      
      return {
        times,
        avgTime,
        firstCallTime,
        cachedCallsAvgTime,
        performanceImprovement: ((firstCallTime - cachedCallsAvgTime) / firstCallTime * 100).toFixed(1)
      }
    } catch (error) {
      console.error('❌ 性能对比测试失败:', error)
      throw error
    }
  }
}

// 导出测试函数
export default testMigration
