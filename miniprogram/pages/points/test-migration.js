/**
 * 积分页面迁移测试
 * 用于验证新API系统在积分页面的功能
 */

import { api } from '../../core/api/index.js'

/**
 * 测试积分页面的各种功能
 */
export const testPointsMigration = {
  
  /**
   * 测试获取积分余额
   */
  async testGetPointsBalance() {
    console.log('=== 测试获取积分余额 ===')
    
    try {
      const result = await api.points.getPointsBalance()
      console.log('✅ 获取积分余额成功:', result)
      
      if (result.success && result.data) {
        console.log(`当前积分余额: ${result.data.balance}`)
      }
      
      return result
    } catch (error) {
      console.error('❌ 获取积分余额失败:', error)
      throw error
    }
  },

  /**
   * 测试获取积分记录
   */
  async testGetPointsRecords() {
    console.log('=== 测试获取积分记录 ===')
    
    try {
      const result = await api.points.getPointsRecords()
      console.log('✅ 获取积分记录成功:', result)
      
      if (result.success && result.data) {
        console.log(`积分记录数量: ${result.data.records.length}`)
      }
      
      return result
    } catch (error) {
      console.error('❌ 获取积分记录失败:', error)
      throw error
    }
  },

  /**
   * 测试分页获取积分记录
   */
  async testGetPointsRecordsByPage() {
    console.log('=== 测试分页获取积分记录 ===')
    
    try {
      // 测试第一页
      const page1Result = await api.points.getPointsRecordsByPage(1, 10)
      console.log('✅ 第一页积分记录获取成功:', page1Result)
      
      // 测试第二页
      const page2Result = await api.points.getPointsRecordsByPage(2, 10)
      console.log('✅ 第二页积分记录获取成功:', page2Result)
      
      return { page1Result, page2Result }
    } catch (error) {
      console.error('❌ 分页获取积分记录失败:', error)
      throw error
    }
  },

  /**
   * 测试按类型获取积分记录
   */
  async testGetPointsRecordsByType() {
    console.log('=== 测试按类型获取积分记录 ===')
    
    try {
      // 测试获取收入记录
      const earnResult = await api.points.getPointsRecordsByType('earn')
      console.log('✅ 获取收入记录成功:', earnResult)
      
      // 测试获取支出记录
      const spendResult = await api.points.getPointsRecordsByType('spend')
      console.log('✅ 获取支出记录成功:', spendResult)
      
      return { earnResult, spendResult }
    } catch (error) {
      console.error('❌ 按类型获取积分记录失败:', error)
      throw error
    }
  },

  /**
   * 测试获取积分统计
   */
  async testGetPointsStats() {
    console.log('=== 测试获取积分统计 ===')
    
    try {
      const result = await api.points.getPointsStats()
      console.log('✅ 获取积分统计成功:', result)
      
      if (result.success && result.data) {
        console.log('积分统计信息:', result.data)
      }
      
      return result
    } catch (error) {
      console.error('❌ 获取积分统计失败:', error)
      throw error
    }
  },

  /**
   * 测试缓存功能
   */
  async testCacheFeature() {
    console.log('=== 测试积分缓存功能 ===')
    
    try {
      // 清除缓存
      api.points.clearPointsCache('all')
      console.log('已清除所有积分缓存')

      // 第一次调用（网络请求）
      console.log('第一次获取积分记录（网络请求）...')
      const start1 = Date.now()
      const result1 = await api.points.getPointsRecords()
      const time1 = Date.now() - start1
      console.log(`✅ 第一次调用成功，耗时: ${time1}ms`)

      // 第二次调用（缓存获取）
      console.log('第二次获取积分记录（缓存获取）...')
      const start2 = Date.now()
      const result2 = await api.points.getPointsRecords()
      const time2 = Date.now() - start2
      console.log(`✅ 第二次调用成功，耗时: ${time2}ms`)

      // 比较耗时
      if (time2 < time1) {
        console.log(`🚀 缓存生效！速度提升: ${((time1 - time2) / time1 * 100).toFixed(1)}%`)
      }

      return { result1, result2, time1, time2 }
    } catch (error) {
      console.error('❌ 缓存测试失败:', error)
      throw error
    }
  },

  /**
   * 测试不同类型的缓存
   */
  async testDifferentTypeCache() {
    console.log('=== 测试不同类型缓存 ===')
    
    try {
      console.log('测试不同类型的独立缓存...')
      
      // 获取收入记录（独立缓存）
      const earnResult = await api.points.getPointsRecordsByType('earn')
      console.log('✅ 收入记录获取成功')
      
      // 获取支出记录（独立缓存）
      const spendResult = await api.points.getPointsRecordsByType('spend')
      console.log('✅ 支出记录获取成功')
      
      // 获取全部记录（独立缓存）
      const allResult = await api.points.getPointsRecords()
      console.log('✅ 全部记录获取成功')
      
      return { earnResult, spendResult, allResult }
    } catch (error) {
      console.error('❌ 不同类型缓存测试失败:', error)
      throw error
    }
  },

  /**
   * 测试缓存管理
   */
  async testCacheManagement() {
    console.log('=== 测试缓存管理 ===')
    
    try {
      // 获取缓存统计
      const statsBefore = api.getStats()
      console.log('缓存统计（清理前）:', statsBefore)

      // 清除特定类型缓存
      api.points.clearPointsCache('recordsByType', 'earn')
      console.log('✅ 清除收入记录缓存成功')

      // 清除所有积分缓存
      api.points.clearPointsCache('all')
      console.log('✅ 清除所有积分缓存成功')

      // 获取清理后的统计
      const statsAfter = api.getStats()
      console.log('缓存统计（清理后）:', statsAfter)

      return { statsBefore, statsAfter }
    } catch (error) {
      console.error('❌ 缓存管理测试失败:', error)
      throw error
    }
  },

  /**
   * 测试并行加载
   */
  async testParallelLoading() {
    console.log('=== 测试并行加载 ===')
    
    try {
      console.log('开始并行加载所有积分数据...')
      const start = Date.now()
      
      const [recordsResult, statsResult, balanceResult] = await Promise.all([
        api.points.getPointsRecords({ showLoading: false }),
        api.points.getPointsStats({ showLoading: false }),
        api.points.getPointsBalance({ showLoading: false })
      ])
      
      const totalTime = Date.now() - start
      console.log(`✅ 并行加载完成，总耗时: ${totalTime}ms`)
      
      return {
        recordsResult,
        statsResult,
        balanceResult,
        totalTime
      }
    } catch (error) {
      console.error('❌ 并行加载测试失败:', error)
      throw error
    }
  },

  /**
   * 测试积分变动处理
   */
  async testPointsChangeHandling() {
    console.log('=== 测试积分变动处理 ===')
    
    try {
      // 模拟积分变动
      console.log('模拟积分变动，清除相关缓存...')
      api.points.onPointsChanged()
      
      console.log('✅ 积分变动处理成功')
      
      // 验证缓存已清除
      const stats = api.getStats()
      console.log('积分变动后缓存统计:', stats)
      
      return { success: true, stats }
    } catch (error) {
      console.error('❌ 积分变动处理测试失败:', error)
      throw error
    }
  },

  /**
   * 运行所有测试
   */
  async runAllTests() {
    console.log('🚀 开始运行积分页面迁移测试...')
    
    const results = {}
    
    try {
      // 基础功能测试
      results.pointsBalance = await this.testGetPointsBalance()
      results.pointsRecords = await this.testGetPointsRecords()
      results.pointsRecordsByPage = await this.testGetPointsRecordsByPage()
      results.pointsRecordsByType = await this.testGetPointsRecordsByType()
      results.pointsStats = await this.testGetPointsStats()
      
      // 缓存功能测试
      results.cacheFeature = await this.testCacheFeature()
      results.differentTypeCache = await this.testDifferentTypeCache()
      results.cacheManagement = await this.testCacheManagement()
      
      // 高级功能测试
      results.parallelLoading = await this.testParallelLoading()
      results.pointsChangeHandling = await this.testPointsChangeHandling()
      
      console.log('🎉 所有积分测试完成！')
      console.log('测试结果汇总:', results)
      
      return results
    } catch (error) {
      console.error('💥 测试过程中出现错误:', error)
      throw error
    }
  },

  /**
   * 性能对比测试
   */
  async performanceComparison() {
    console.log('=== 积分页面性能对比测试 ===')
    
    try {
      // 清除所有缓存
      api.points.clearPointsCache('all')
      
      // 测试多次调用的性能
      const iterations = 3
      const times = []
      
      for (let i = 0; i < iterations; i++) {
        const start = Date.now()
        await api.points.getPointsRecords()
        const time = Date.now() - start
        times.push(time)
        console.log(`第 ${i + 1} 次调用耗时: ${time}ms`)
      }
      
      const firstCallTime = times[0]
      const cachedCallsAvgTime = times.slice(1).reduce((a, b) => a + b, 0) / (times.length - 1)
      
      console.log(`首次调用耗时: ${firstCallTime}ms`)
      console.log(`缓存调用平均耗时: ${cachedCallsAvgTime.toFixed(1)}ms`)
      console.log(`性能提升: ${((firstCallTime - cachedCallsAvgTime) / firstCallTime * 100).toFixed(1)}%`)
      
      return {
        times,
        firstCallTime,
        cachedCallsAvgTime,
        performanceImprovement: ((firstCallTime - cachedCallsAvgTime) / firstCallTime * 100).toFixed(1)
      }
    } catch (error) {
      console.error('❌ 性能对比测试失败:', error)
      throw error
    }
  }
}

// 导出测试函数
export default testPointsMigration
