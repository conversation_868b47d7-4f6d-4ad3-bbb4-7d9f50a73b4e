/**
 * 签到页面迁移测试
 * 用于验证新API系统在签到页面的功能
 */

import { api } from '../../core/api/index.js'

/**
 * 测试签到页面的各种功能
 */
export const testCheckInMigration = {
  
  /**
   * 测试获取签到状态
   */
  async testGetCheckInStatus() {
    console.log('=== 测试获取签到状态 ===')
    
    try {
      const result = await api.checkIn.getCheckInStatus()
      console.log('✅ 获取签到状态成功:', result)
      
      // 验证数据结构
      if (result.success && result.data) {
        const { hasCheckedInToday, consecutiveDays, totalDays } = result.data
        console.log(`签到状态: 今日${hasCheckedInToday ? '已' : '未'}签到, 连续${consecutiveDays}天, 累计${totalDays}天`)
      }
      
      return result
    } catch (error) {
      console.error('❌ 获取签到状态失败:', error)
      throw error
    }
  },

  /**
   * 测试获取签到日历
   */
  async testGetCheckInCalendar() {
    console.log('=== 测试获取签到日历 ===')
    
    try {
      const now = new Date()
      const yearMonth = `${now.getFullYear()}-${String(now.getMonth() + 1).padStart(2, '0')}`
      
      console.log(`获取 ${yearMonth} 的签到日历...`)
      const result = await api.checkIn.getCheckInCalendar(yearMonth)
      console.log('✅ 获取签到日历成功:', result)
      
      if (result.success && result.data) {
        console.log(`本月签到记录: ${result.data.checkIns.length}条`)
      }
      
      return result
    } catch (error) {
      console.error('❌ 获取签到日历失败:', error)
      throw error
    }
  },

  /**
   * 测试获取签到历史
   */
  async testGetCheckInHistory() {
    console.log('=== 测试获取签到历史 ===')
    
    try {
      const result = await api.checkIn.getCheckInHistory({ limit: 10 })
      console.log('✅ 获取签到历史成功:', result)
      
      if (result.success && result.data) {
        console.log(`签到历史记录: ${result.data.history.length}条`)
      }
      
      return result
    } catch (error) {
      console.error('❌ 获取签到历史失败:', error)
      throw error
    }
  },

  /**
   * 测试缓存功能
   */
  async testCacheFeature() {
    console.log('=== 测试签到缓存功能 ===')
    
    try {
      // 清除缓存
      api.checkIn.clearCheckInCache('all')
      console.log('已清除所有签到缓存')

      // 第一次调用（网络请求）
      console.log('第一次获取签到状态（网络请求）...')
      const start1 = Date.now()
      const result1 = await api.checkIn.getCheckInStatus()
      const time1 = Date.now() - start1
      console.log(`✅ 第一次调用成功，耗时: ${time1}ms`)

      // 第二次调用（缓存获取）
      console.log('第二次获取签到状态（缓存获取）...')
      const start2 = Date.now()
      const result2 = await api.checkIn.getCheckInStatus()
      const time2 = Date.now() - start2
      console.log(`✅ 第二次调用成功，耗时: ${time2}ms`)

      // 比较耗时
      if (time2 < time1) {
        console.log(`🚀 缓存生效！速度提升: ${((time1 - time2) / time1 * 100).toFixed(1)}%`)
      }

      return { result1, result2, time1, time2 }
    } catch (error) {
      console.error('❌ 缓存测试失败:', error)
      throw error
    }
  },

  /**
   * 测试不同月份的日历缓存
   */
  async testCalendarCache() {
    console.log('=== 测试日历缓存 ===')
    
    try {
      const now = new Date()
      const currentMonth = `${now.getFullYear()}-${String(now.getMonth() + 1).padStart(2, '0')}`
      const lastMonth = `${now.getFullYear()}-${String(now.getMonth()).padStart(2, '0')}`
      
      console.log('测试不同月份的独立缓存...')
      
      // 获取当前月份日历
      const currentResult = await api.checkIn.getCheckInCalendar(currentMonth)
      console.log(`✅ 当前月份 ${currentMonth} 日历获取成功`)
      
      // 获取上个月份日历
      const lastResult = await api.checkIn.getCheckInCalendar(lastMonth)
      console.log(`✅ 上个月份 ${lastMonth} 日历获取成功`)
      
      return { currentResult, lastResult }
    } catch (error) {
      console.error('❌ 日历缓存测试失败:', error)
      throw error
    }
  },

  /**
   * 测试缓存管理
   */
  async testCacheManagement() {
    console.log('=== 测试缓存管理 ===')
    
    try {
      // 获取缓存统计
      const statsBefore = api.getStats()
      console.log('缓存统计（清理前）:', statsBefore)

      // 清除特定缓存
      api.checkIn.clearCheckInCache('status')
      console.log('✅ 清除签到状态缓存成功')

      api.checkIn.clearCheckInCache('calendar')
      console.log('✅ 清除签到日历缓存成功')

      // 清除所有签到缓存
      api.checkIn.clearCheckInCache('all')
      console.log('✅ 清除所有签到缓存成功')

      // 获取清理后的统计
      const statsAfter = api.getStats()
      console.log('缓存统计（清理后）:', statsAfter)

      return { statsBefore, statsAfter }
    } catch (error) {
      console.error('❌ 缓存管理测试失败:', error)
      throw error
    }
  },

  /**
   * 测试并行加载
   */
  async testParallelLoading() {
    console.log('=== 测试并行加载 ===')
    
    try {
      const now = new Date()
      const yearMonth = `${now.getFullYear()}-${String(now.getMonth() + 1).padStart(2, '0')}`
      
      console.log('开始并行加载所有签到数据...')
      const start = Date.now()
      
      const [statusResult, calendarResult, historyResult] = await Promise.all([
        api.checkIn.getCheckInStatus({ showLoading: false }),
        api.checkIn.getCheckInCalendar(yearMonth, { showLoading: false }),
        api.checkIn.getCheckInHistory({ limit: 10 }, { showLoading: false })
      ])
      
      const totalTime = Date.now() - start
      console.log(`✅ 并行加载完成，总耗时: ${totalTime}ms`)
      
      return {
        statusResult,
        calendarResult,
        historyResult,
        totalTime
      }
    } catch (error) {
      console.error('❌ 并行加载测试失败:', error)
      throw error
    }
  },

  /**
   * 模拟签到流程测试（仅测试API调用，不实际签到）
   */
  async testCheckInFlow() {
    console.log('=== 测试签到流程 ===')
    
    try {
      // 1. 获取签到状态
      const statusResult = await api.checkIn.getCheckInStatus()
      console.log('✅ 获取签到状态成功')
      
      if (statusResult.success && statusResult.data.hasCheckedInToday) {
        console.log('ℹ️ 今日已签到，跳过签到测试')
        return { alreadyCheckedIn: true, statusResult }
      }
      
      // 注意：这里不实际执行签到，只是测试API调用
      console.log('ℹ️ 今日未签到，但为了测试安全，不执行实际签到')
      console.log('ℹ️ 如需测试实际签到，请手动调用 api.checkIn.checkIn()')
      
      return { alreadyCheckedIn: false, statusResult }
    } catch (error) {
      console.error('❌ 签到流程测试失败:', error)
      throw error
    }
  },

  /**
   * 运行所有测试
   */
  async runAllTests() {
    console.log('🚀 开始运行签到页面迁移测试...')
    
    const results = {}
    
    try {
      // 基础功能测试
      results.checkInStatus = await this.testGetCheckInStatus()
      results.checkInCalendar = await this.testGetCheckInCalendar()
      results.checkInHistory = await this.testGetCheckInHistory()
      
      // 缓存功能测试
      results.cacheFeature = await this.testCacheFeature()
      results.calendarCache = await this.testCalendarCache()
      results.cacheManagement = await this.testCacheManagement()
      
      // 高级功能测试
      results.parallelLoading = await this.testParallelLoading()
      results.checkInFlow = await this.testCheckInFlow()
      
      console.log('🎉 所有签到测试完成！')
      console.log('测试结果汇总:', results)
      
      return results
    } catch (error) {
      console.error('💥 测试过程中出现错误:', error)
      throw error
    }
  },

  /**
   * 性能对比测试
   */
  async performanceComparison() {
    console.log('=== 签到页面性能对比测试 ===')
    
    try {
      // 清除所有缓存
      api.checkIn.clearCheckInCache('all')
      
      // 测试多次调用的性能
      const iterations = 3
      const times = []
      
      for (let i = 0; i < iterations; i++) {
        const start = Date.now()
        await api.checkIn.getCheckInStatus()
        const time = Date.now() - start
        times.push(time)
        console.log(`第 ${i + 1} 次调用耗时: ${time}ms`)
      }
      
      const firstCallTime = times[0]
      const cachedCallsAvgTime = times.slice(1).reduce((a, b) => a + b, 0) / (times.length - 1)
      
      console.log(`首次调用耗时: ${firstCallTime}ms`)
      console.log(`缓存调用平均耗时: ${cachedCallsAvgTime.toFixed(1)}ms`)
      console.log(`性能提升: ${((firstCallTime - cachedCallsAvgTime) / firstCallTime * 100).toFixed(1)}%`)
      
      return {
        times,
        firstCallTime,
        cachedCallsAvgTime,
        performanceImprovement: ((firstCallTime - cachedCallsAvgTime) / firstCallTime * 100).toFixed(1)
      }
    } catch (error) {
      console.error('❌ 性能对比测试失败:', error)
      throw error
    }
  }
}

// 导出测试函数
export default testCheckInMigration
