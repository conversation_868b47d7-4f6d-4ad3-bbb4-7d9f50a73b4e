// 调试工具页面
Page({
  data: {
    // 展开状态
    systemExpanded: true,
    storageExpanded: false,
    dataExpanded: false,
    managersExpanded: false,
    testingExpanded: false,
    logsExpanded: false,
    toolsExpanded: false,

    // 系统信息
    systemInfo: {},
    performanceInfo: {},
    appInfo: {
      appId: '',
      version: '',
      envVersion: '',
      envVersionText: ''
    },

    // 存储信息
    storageInfo: {},
    showStorageKeys: false,
    storageKeysText: '',

    // 数据状态
    dataStatus: {
      loaded: false,
      workCount: 0,
      timeSegmentCount: 0
    },
    showUserData: false,
    userDataText: '',

    // 管理器状态
    managerStatus: [],

    // 测试结果
    testResults: '',

    // 日志
    logs: [],

    // 工具
    customCommand: '',

    // 缓存管理
    cacheExpanded: false,
    cacheInfo: {
      totalSize: 0,
      itemCount: 0,
      items: []
    },

    // 更新检测
    updateExpanded: false,
    updateInfo: {
      hasUpdate: false,
      updateAvailable: false
    },

    // 错误监控
    errorExpanded: false,
    errorInfo: {
      errorCount: 0,
      errors: []
    },

    // 数据压缩分析
    compressionExpanded: false,
    compressionInfo: {
      lastTest: null,
      history: []
    },

    // 网络诊断（仅保留基本状态用于系统信息显示）
    networkInfo: {
      status: 'unknown'
    },

    // 文件系统
    fileSystemExpanded: false,
    fileSystemInfo: {
      files: [],
      totalSize: 0,
      fileCount: 0
    },

    // 加载状态
    loading: false,
    loadingText: '处理中...'
  },

  /**
   * 页面加载时
   */
  onLoad() {
    console.log('调试页面加载')
    this.initializeDebugPage()
  },

  /**
   * 页面显示时
   */
  onShow() {
    console.log('调试页面显示')
    this.refreshAllData()
  },

  /**
   * 下拉刷新
   */
  onPullDownRefresh() {
    console.log('下拉刷新调试页面')
    this.refreshAllData()
    setTimeout(() => {
      wx.stopPullDownRefresh()
    }, 1000)
  },

  /**
   * 初始化调试页面
   */
  initializeDebugPage() {
    // 获取全局管理器
    const app = getApp()
    this.dataManager = app.getDataManager()
    this.userManager = app.getUserManager()
    this.syncManager = app.getSyncManager()
    this.storageManager = app.getStorageManager()
    this.workManager = app.getWorkManager()
    this.settingsManager = app.getSettingsManager()
    this.timeTrackingManager = app.getTimeTrackingManager()

    // 初始化日志系统
    this.initializeLogger()

    // 初始化错误监控
    this.initializeErrorMonitoring()

    // 加载初始数据
    this.loadSystemInfo()
    this.loadStorageInfo()
    this.loadDataStatus()
    this.loadManagerStatus()
    this.loadCacheInfo()

    // 初始化网络状态（仅用于系统信息显示）
    this.loadNetworkStatus()

    this.addLog('info', '调试页面初始化完成')
  },

  /**
   * 刷新所有数据
   */
  refreshAllData() {
    this.loadSystemInfo()
    this.loadStorageInfo()
    this.loadDataStatus()
    this.loadManagerStatus()
    this.loadCacheInfo()
    this.loadNetworkStatus()
    this.addLog('info', '所有数据已刷新')
  },

  /**
   * 初始化日志系统
   */
  initializeLogger() {
    // 重写console方法来捕获日志
    const originalLog = console.log
    const originalError = console.error
    const originalWarn = console.warn

    console.log = (...args) => {
      originalLog.apply(console, args)
      this.addLog('info', args.join(' '))
    }

    console.error = (...args) => {
      originalError.apply(console, args)
      this.addLog('error', args.join(' '))
    }

    console.warn = (...args) => {
      originalWarn.apply(console, args)
      this.addLog('warn', args.join(' '))
    }
  },

  /**
   * 添加日志
   */
  addLog(level, message) {
    const logs = this.data.logs
    const now = new Date()
    const time = `${now.getHours().toString().padStart(2, '0')}:${now.getMinutes().toString().padStart(2, '0')}:${now.getSeconds().toString().padStart(2, '0')}`
    
    logs.unshift({
      time,
      level,
      message: String(message)
    })

    // 限制日志数量
    if (logs.length > 100) {
      logs.splice(100)
    }

    this.setData({ logs })
  },

  /**
   * 加载系统信息
   */
  loadSystemInfo() {
    try {
      // 使用新的API替换已废弃的 wx.getSystemInfoSync
      const deviceInfo = wx.getDeviceInfo()
      const windowInfo = wx.getWindowInfo()
      const appBaseInfo = wx.getAppBaseInfo()

      // 合并系统信息，保持与原 systemInfo 相同的数据结构
      const systemInfo = {
        ...deviceInfo,
        ...windowInfo,
        ...appBaseInfo
      }

      // 获取性能信息
      let performanceInfo = {}
      if (wx.getPerformance) {
        const performance = wx.getPerformance()
        if (performance.memory) {
          performanceInfo = {
            usedJSHeapSize: Math.round(performance.memory.usedJSHeapSize / 1024 / 1024),
            totalJSHeapSize: Math.round(performance.memory.totalJSHeapSize / 1024 / 1024),
            jsHeapSizeLimit: Math.round(performance.memory.jsHeapSizeLimit / 1024 / 1024)
          }
        }

        // 获取启动时间信息
        if (performance.timing) {
          const timing = performance.timing
          performanceInfo.navigationStart = timing.navigationStart
          performanceInfo.loadEventEnd = timing.loadEventEnd
          performanceInfo.startupTime = timing.loadEventEnd - timing.navigationStart
        }
      }

      // 获取账号信息
      let appInfo = {
        appId: '未知',
        version: '未知',
        envVersion: '未知',
        envVersionText: '未知'
      }

      try {
        if (wx.getAccountInfoSync) {
          const accountInfo = wx.getAccountInfoSync()
          if (accountInfo && accountInfo.miniProgram) {
            const miniProgram = accountInfo.miniProgram
            appInfo = {
              appId: miniProgram.appId || '未知',
              version: miniProgram.version || '未知',
              envVersion: miniProgram.envVersion || '未知',
              envVersionText: this.getEnvVersionText(miniProgram.envVersion)
            }
          }
        }
      } catch (accountError) {
        console.warn('获取账号信息失败:', accountError)
        this.addLog('warn', `获取账号信息失败: ${accountError.message}`)
      }

      this.setData({
        systemInfo,
        performanceInfo,
        appInfo
      })

      this.addLog('info', '系统信息加载完成')
    } catch (error) {
      this.addLog('error', `加载系统信息失败: ${error.message}`)
    }
  },

  /**
   * 获取环境版本文本描述
   */
  getEnvVersionText(envVersion) {
    const envMap = {
      'develop': '开发版',
      'trial': '体验版',
      'release': '正式版'
    }
    return envMap[envVersion] || envVersion || '未知'
  },

  /**
   * 加载网络状态（简化版，仅用于系统信息显示）
   */
  loadNetworkStatus() {
    try {
      wx.getNetworkType({
        success: (res) => {
          this.setData({
            'networkInfo.status': res.networkType
          })
        },
        fail: () => {
          this.setData({
            'networkInfo.status': '未知'
          })
        }
      })
    } catch (error) {
      this.addLog('error', `获取网络状态失败: ${error.message}`)
    }
  },

  /**
   * 检查API兼容性
   */
  checkApiCompatibility() {
    const compatibility = {
      getAccountInfoSync: !!wx.getAccountInfoSync,
      getPerformance: !!wx.getPerformance,
      cloud: !!wx.cloud,
      getStorageInfo: !!wx.getStorageInfoSync,
      setClipboardData: !!wx.setClipboardData,
      showActionSheet: !!wx.showActionSheet,
      reLaunch: !!wx.reLaunch
    }

    const compatibilityText = Object.entries(compatibility)
      .map(([api, supported]) => `${api}: ${supported ? '✓' : '✗'}`)
      .join('\n')

    wx.showModal({
      title: 'API兼容性检查',
      content: `基础库版本: ${this.data.systemInfo.SDKVersion}\n\nAPI支持情况:\n${compatibilityText}`,
      showCancel: false,
      confirmText: '知道了'
    })

    this.addLog('info', 'API兼容性检查完成')
  },

  /**
   * 检查网络状态
   */
  checkNetworkStatus() {
    try {
      wx.getNetworkType({
        success: (res) => {
          const networkType = res.networkType
          let networkDesc = ''

          switch (networkType) {
            case 'wifi':
              networkDesc = 'WiFi网络'
              break
            case '2g':
              networkDesc = '2G网络'
              break
            case '3g':
              networkDesc = '3G网络'
              break
            case '4g':
              networkDesc = '4G网络'
              break
            case '5g':
              networkDesc = '5G网络'
              break
            case 'none':
              networkDesc = '无网络连接'
              break
            default:
              networkDesc = '未知网络类型'
          }

          wx.showModal({
            title: '网络状态',
            content: `网络类型: ${networkDesc}\n连接状态: ${networkType === 'none' ? '离线' : '在线'}`,
            showCancel: false,
            confirmText: '知道了'
          })

          this.addLog('info', `网络状态检查完成: ${networkDesc}`)
        },
        fail: (error) => {
          wx.showToast({
            title: '获取网络状态失败',
            icon: 'error'
          })
          this.addLog('error', `获取网络状态失败: ${error.message}`)
        }
      })
    } catch (error) {
      this.addLog('error', `网络状态检查失败: ${error.message}`)
    }
  },

  // ==================== 数据管理相关方法 ====================

  /**
   * 查看页面详情
   */
  viewPageDetails() {
    try {
      const pages = getCurrentPages()
      const currentPage = pages[pages.length - 1]

      const details = {
        route: currentPage.route,
        options: currentPage.options,
        data: Object.keys(currentPage.data || {}),
        pageStack: pages.map(p => p.route)
      }

      const detailsText = JSON.stringify(details, null, 2)

      wx.showModal({
        title: '当前页面详情',
        content: detailsText,
        showCancel: false,
        confirmText: '知道了'
      })
    } catch (error) {
      this.addLog('error', `查看页面详情失败: ${error.message}`)
    }
  },

  /**
   * 测试页面跳转
   */
  testNavigation() {
    wx.showActionSheet({
      itemList: [
        '跳转到首页',
        '跳转到个人页面',
        '跳转到工作履历',
        '跳转到统计页面',
        '返回上一页'
      ],
      success: (res) => {
        switch (res.tapIndex) {
          case 0:
            wx.switchTab({ url: '/pages/index/index' })
            break
          case 1:
            wx.switchTab({ url: '/pages/profile/index' })
            break
          case 2:
            wx.switchTab({ url: '/pages/work-history/index' })
            break
          case 3:
            wx.switchTab({ url: '/pages/statistics/index' })
            break
          case 4:
            wx.navigateBack()
            break
        }
        this.addLog('info', `测试页面跳转: ${res.tapIndex}`)
      }
    })
  },

  /**
   * 清空页面栈
   */
  clearPageStack() {
    wx.showModal({
      title: '清空页面栈',
      content: '这将重新启动应用并清空所有页面栈，确定继续吗？',
      success: (res) => {
        if (res.confirm) {
          wx.reLaunch({
            url: '/pages/index/index'
          })
        }
      }
    })
  },

  // ==================== 缓存管理相关方法 ====================

  /**
   * 加载缓存信息
   */
  loadCacheInfo() {
    try {
      const storageInfo = wx.getStorageInfoSync()
      const cacheItems = []

      // 获取所有存储项的详细信息
      storageInfo.keys.forEach(key => {
        try {
          const data = wx.getStorageSync(key)
          const size = JSON.stringify(data).length
          cacheItems.push({
            key: key,
            size: size,
            type: typeof data,
            preview: this.getDataPreview(data)
          })
        } catch (error) {
          cacheItems.push({
            key: key,
            size: 0,
            type: 'error',
            preview: '读取失败'
          })
        }
      })

      // 按大小排序
      cacheItems.sort((a, b) => b.size - a.size)

      this.setData({
        'cacheInfo.totalSize': storageInfo.currentSize,
        'cacheInfo.itemCount': storageInfo.keys.length,
        'cacheInfo.items': cacheItems
      })

      this.addLog('info', '缓存信息加载完成')
    } catch (error) {
      this.addLog('error', `加载缓存信息失败: ${error.message}`)
    }
  },

  /**
   * 获取数据预览
   */
  getDataPreview(data) {
    if (data === null || data === undefined) {
      return 'null'
    }

    const str = JSON.stringify(data)
    return str.length > 50 ? str.substring(0, 50) + '...' : str
  },

  /**
   * 查看缓存项详情
   */
  viewCacheItem(e) {
    try {
      const key = e.currentTarget.dataset.key
      const data = wx.getStorageSync(key)
      const dataText = JSON.stringify(data, null, 2)

      wx.showModal({
        title: `缓存项: ${key}`,
        content: `类型: ${typeof data}\n大小: ${dataText.length} 字符\n\n内容:\n${dataText}`,
        showCancel: false,
        confirmText: '知道了'
      })
    } catch (error) {
      this.addLog('error', `查看缓存项失败: ${error.message}`)
    }
  },

  /**
   * 删除缓存项
   */
  removeCacheItem(e) {
    const key = e.currentTarget.dataset.key
    wx.showModal({
      title: '删除缓存项',
      content: `确定要删除缓存项 "${key}" 吗？`,
      success: (res) => {
        if (res.confirm) {
          try {
            wx.removeStorageSync(key)
            this.loadCacheInfo()
            wx.showToast({
              title: '删除成功',
              icon: 'success'
            })
            this.addLog('info', `删除缓存项: ${key}`)
          } catch (error) {
            wx.showToast({
              title: '删除失败',
              icon: 'error'
            })
            this.addLog('error', `删除缓存项失败: ${error.message}`)
          }
        }
      }
    })
  },

  /**
   * 清空所有缓存
   */
  clearAllCache() {
    wx.showModal({
      title: '清空所有缓存',
      content: '这将删除所有本地存储数据，包括用户数据！此操作不可恢复，确定继续吗？',
      success: (res) => {
        if (res.confirm) {
          try {
            wx.clearStorageSync()
            this.loadCacheInfo()
            wx.showToast({
              title: '缓存已清空',
              icon: 'success'
            })
            this.addLog('warn', '所有缓存已清空')
          } catch (error) {
            wx.showToast({
              title: '清空失败',
              icon: 'error'
            })
            this.addLog('error', `清空缓存失败: ${error.message}`)
          }
        }
      }
    })
  },

  // ==================== 权限状态检查相关方法 ====================

  /**
   * 检查所有权限状态
   */
  async checkAllPermissions() {
    try {
      this.setData({ loading: true, loadingText: '检查权限状态中...' })

      const permissions = {
        location: await this.checkPermission('scope.userLocation'),
        camera: await this.checkPermission('scope.camera'),
        album: await this.checkPermission('scope.writePhotosAlbum'),
        record: await this.checkPermission('scope.record'),
        userInfo: await this.checkPermission('scope.userInfo'),
        address: await this.checkPermission('scope.address'),
        invoiceTitle: await this.checkPermission('scope.invoiceTitle'),
        invoice: await this.checkPermission('scope.invoice'),
        werun: await this.checkPermission('scope.werun'),
        bluetooth: await this.checkPermission('scope.bluetooth')
      }

      this.setData({
        permissionInfo: permissions,
        loading: false
      })

      this.addLog('info', '权限状态检查完成')
    } catch (error) {
      this.setData({ loading: false })
      this.addLog('error', `检查权限状态失败: ${error.message}`)
    }
  },

  /**
   * 检查单个权限
   */
  checkPermission(scope) {
    return new Promise((resolve) => {
      wx.getSetting({
        success: (res) => {
          const status = res.authSetting[scope]
          if (status === true) {
            resolve('已授权')
          } else if (status === false) {
            resolve('已拒绝')
          } else {
            resolve('未询问')
          }
        },
        fail: () => {
          resolve('检查失败')
        }
      })
    })
  },

  /**
   * 申请权限
   */
  requestPermission(e) {
    const scope = e.currentTarget.dataset.scope
    const scopeNames = {
      'scope.userLocation': '位置信息',
      'scope.camera': '摄像头',
      'scope.writePhotosAlbum': '保存到相册',
      'scope.record': '录音',
      'scope.userInfo': '用户信息',
      'scope.address': '通讯地址',
      'scope.invoiceTitle': '发票抬头',
      'scope.invoice': '获取发票',
      'scope.werun': '微信运动步数',
      'scope.bluetooth': '蓝牙'
    }

    const scopeName = scopeNames[scope] || scope

    wx.showModal({
      title: '申请权限',
      content: `是否申请 "${scopeName}" 权限？`,
      success: (res) => {
        if (res.confirm) {
          wx.authorize({
            scope: scope,
            success: () => {
              wx.showToast({
                title: '授权成功',
                icon: 'success'
              })
              this.checkAllPermissions()
              this.addLog('info', `权限授权成功: ${scopeName}`)
            },
            fail: () => {
              wx.showToast({
                title: '授权失败',
                icon: 'error'
              })
              this.addLog('warn', `权限授权失败: ${scopeName}`)
            }
          })
        }
      }
    })
  },

  /**
   * 打开设置页面
   */
  openSettings() {
    wx.openSetting({
      success: (res) => {
        this.addLog('info', '打开设置页面成功')
        // 重新检查权限状态
        setTimeout(() => {
          this.checkAllPermissions()
        }, 1000)
      },
      fail: (error) => {
        this.addLog('error', `打开设置页面失败: ${error.message}`)
      }
    })
  },

  // ==================== 小程序更新检测相关方法 ====================

  /**
   * 检查小程序更新
   */
  checkForUpdates() {
    try {
      if (wx.getUpdateManager) {
        const updateManager = wx.getUpdateManager()

        // 检查更新
        updateManager.onCheckForUpdate((res) => {
          this.setData({
            'updateInfo.hasUpdate': res.hasUpdate,
            'updateInfo.updateAvailable': res.hasUpdate
          })

          if (res.hasUpdate) {
            this.addLog('info', '发现新版本')
            wx.showToast({
              title: '发现新版本',
              icon: 'success'
            })
          } else {
            this.addLog('info', '当前已是最新版本')
            wx.showToast({
              title: '当前已是最新版本',
              icon: 'none'
            })
          }
        })

        // 下载更新
        updateManager.onUpdateReady(() => {
          this.addLog('info', '新版本下载完成')
          wx.showModal({
            title: '更新提示',
            content: '新版本已经准备好，是否重启应用？',
            success: (res) => {
              if (res.confirm) {
                updateManager.applyUpdate()
              }
            }
          })
        })

        // 下载失败
        updateManager.onUpdateFailed(() => {
          this.addLog('error', '新版本下载失败')
          wx.showToast({
            title: '更新失败',
            icon: 'error'
          })
        })

        // 手动检查更新
        updateManager.checkForUpdate()
      } else {
        this.addLog('warn', '当前版本不支持更新检查')
        wx.showToast({
          title: '不支持更新检查',
          icon: 'none'
        })
      }
    } catch (error) {
      this.addLog('error', `检查更新失败: ${error.message}`)
    }
  },

  /**
   * 强制重启应用
   */
  forceRestart() {
    wx.showModal({
      title: '强制重启',
      content: '确定要重启应用吗？未保存的数据可能会丢失。',
      success: (res) => {
        if (res.confirm) {
          wx.reLaunch({
            url: '/pages/index/index'
          })
        }
      }
    })
  },

  /**
   * 查看版本信息
   */
  viewVersionInfo() {
    try {
      const appInfo = this.data.appInfo
      const systemInfo = this.data.systemInfo

      const versionInfo = `当前版本信息：
小程序版本: ${appInfo.version}
运行环境: ${appInfo.envVersionText}
基础库版本: ${systemInfo.SDKVersion}
微信版本: ${systemInfo.version}
系统版本: ${systemInfo.system}

更新状态:
有可用更新: ${this.data.updateInfo.hasUpdate ? '是' : '否'}
支持更新检查: ${wx.getUpdateManager ? '是' : '否'}`

      wx.showModal({
        title: '版本信息',
        content: versionInfo,
        showCancel: false,
        confirmText: '知道了'
      })
    } catch (error) {
      this.addLog('error', `查看版本信息失败: ${error.message}`)
    }
  },

  /**
   * 加载存储信息
   */
  loadStorageInfo() {
    try {
      const storageInfo = wx.getStorageInfoSync()
      this.setData({ storageInfo })
      this.addLog('info', '存储信息加载完成')
    } catch (error) {
      this.addLog('error', `加载存储信息失败: ${error.message}`)
    }
  },

  /**
   * 加载数据状态
   */
  async loadDataStatus() {
    try {
      await this.dataManager.ensureLoaded()
      
      const userData = this.dataManager.getUserData()
      const workHistory = userData.workHistory || {}
      const timeSegments = userData.timeSegments || {}

      this.setData({
        dataStatus: {
          loaded: true,
          workCount: Object.keys(workHistory).length,
          timeSegmentCount: Object.keys(timeSegments).length
        }
      })

      this.addLog('info', '数据状态加载完成')
    } catch (error) {
      this.setData({
        'dataStatus.loaded': false
      })
      this.addLog('error', `加载数据状态失败: ${error.message}`)
    }
  },

  /**
   * 加载管理器状态
   */
  loadManagerStatus() {
    try {
      const managers = [
        { name: 'DataManager', instance: this.dataManager },
        { name: 'UserManager', instance: this.userManager },
        { name: 'SyncManager', instance: this.syncManager },
        { name: 'StorageManager', instance: this.storageManager },
        { name: 'WorkManager', instance: this.workManager },
        { name: 'SettingsManager', instance: this.settingsManager },
        { name: 'TimeTrackingManager', instance: this.timeTrackingManager }
      ]

      const managerStatus = managers.map(manager => ({
        name: manager.name,
        status: manager.instance ? 'active' : 'inactive'
      }))

      this.setData({ managerStatus })
      this.addLog('info', '管理器状态加载完成')
    } catch (error) {
      this.addLog('error', `加载管理器状态失败: ${error.message}`)
    }
  },

  /**
   * 切换区域展开状态
   */
  toggleSection(e) {
    const section = e.currentTarget.dataset.section
    const key = `${section}Expanded`
    this.setData({
      [key]: !this.data[key]
    })
  },

  /**
   * 刷新存储信息
   */
  refreshStorageInfo() {
    this.loadStorageInfo()
    wx.showToast({
      title: '存储信息已刷新',
      icon: 'success'
    })
  },

  /**
   * 查看存储键
   */
  viewStorageKeys() {
    try {
      const storageInfo = wx.getStorageInfoSync()
      const keysText = storageInfo.keys.join('\n')
      this.setData({
        showStorageKeys: !this.data.showStorageKeys,
        storageKeysText: keysText
      })
    } catch (error) {
      this.addLog('error', `查看存储键失败: ${error.message}`)
    }
  },

  /**
   * 查看用户数据
   */
  async viewUserData() {
    try {
      await this.dataManager.ensureLoaded()
      const userData = this.dataManager.getUserData()
      const userDataText = JSON.stringify(userData, null, 2)
      
      this.setData({
        showUserData: !this.data.showUserData,
        userDataText
      })
    } catch (error) {
      this.addLog('error', `查看用户数据失败: ${error.message}`)
    }
  },

  /**
   * 导出用户数据
   */
  async exportUserData() {
    try {
      await this.dataManager.ensureLoaded()
      const userData = this.dataManager.getUserData()
      const dataText = JSON.stringify(userData, null, 2)

      wx.setClipboardData({
        data: dataText,
        success: () => {
          wx.showToast({
            title: '数据已复制到剪贴板',
            icon: 'success'
          })
          this.addLog('info', '用户数据已导出到剪贴板')
        }
      })
    } catch (error) {
      this.addLog('error', `导出用户数据失败: ${error.message}`)
    }
  },



  /**
   * 清空所有数据
   */
  clearAllData() {
    wx.showModal({
      title: '危险操作',
      content: '这将清空所有用户数据，包括工作履历、时间段等，此操作不可恢复！\n\n请输入"确认清空"来继续',
      editable: true,
      placeholderText: '请输入确认文本',
      success: (res) => {
        if (res.confirm && res.content === '确认清空') {
          this.doClearAllData()
        } else if (res.confirm) {
          wx.showToast({
            title: '确认文本不正确',
            icon: 'error'
          })
        }
      }
    })
  },

  /**
   * 执行清空所有数据
   */
  async doClearAllData() {
    try {
      this.setData({ loading: true, loadingText: '清空数据中...' })

      // 清空存储
      wx.clearStorageSync()

      // 重新初始化数据管理器
      this.dataManager.initializeData()

      this.setData({ loading: false })
      this.refreshAllData()

      wx.showToast({
        title: '数据已清空',
        icon: 'success'
      })

      this.addLog('warn', '所有数据已清空')
    } catch (error) {
      this.setData({ loading: false })
      this.addLog('error', `清空数据失败: ${error.message}`)
      wx.showToast({
        title: '清空失败',
        icon: 'error'
      })
    }
  },

  /**
   * 刷新管理器状态
   */
  refreshManagerStatus() {
    this.loadManagerStatus()
    wx.showToast({
      title: '管理器状态已刷新',
      icon: 'success'
    })
  },

  /**
   * 查看管理器详情
   */
  viewManagerDetails() {
    try {
      const details = {
        dataManager: {
          isLoaded: this.dataManager.isLoaded,
          userData: !!this.dataManager.userData
        },
        userManager: {
          isLoggedIn: this.userManager.isUserLoggedIn(),
          userInfo: !!this.userManager.getUserInfo()
        },
        syncManager: {
          isSyncing: this.syncManager.isSyncing,
          lastSyncTime: this.syncManager.lastSyncTime
        }
      }

      const detailsText = JSON.stringify(details, null, 2)

      wx.showModal({
        title: '管理器详情',
        content: detailsText,
        showCancel: false,
        confirmText: '知道了'
      })
    } catch (error) {
      this.addLog('error', `查看管理器详情失败: ${error.message}`)
    }
  },

  /**
   * 查看应用详细信息
   */
  viewAppDetails() {
    try {
      const appInfo = this.data.appInfo
      const systemInfo = this.data.systemInfo

      let content = `应用信息：
AppID: ${appInfo.appId}
版本号: ${appInfo.version}
环境: ${appInfo.envVersionText} (${appInfo.envVersion})

系统信息：
设备: ${systemInfo.model}
系统: ${systemInfo.system}
微信版本: ${systemInfo.version}
基础库: ${systemInfo.SDKVersion}

环境说明：
• 开发版：开发者工具中运行
• 体验版：通过体验版二维码打开
• 正式版：线上正式发布版本`

      wx.showModal({
        title: '应用详细信息',
        content: content,
        showCancel: false,
        confirmText: '知道了'
      })
    } catch (error) {
      this.addLog('error', `查看应用详情失败: ${error.message}`)
    }
  },

  // ==================== 功能测试相关方法 ====================

  /**
   * 测试数据保存
   */
  async testDataSave() {
    try {
      this.setData({ loading: true, loadingText: '测试数据保存...' })

      await this.dataManager.ensureLoaded()
      await this.dataManager.saveData()

      this.setData({
        loading: false,
        testResults: '数据保存测试通过 ✓\n保存时间: ' + new Date().toLocaleString()
      })

      this.addLog('info', '数据保存测试完成')
    } catch (error) {
      this.setData({
        loading: false,
        testResults: '数据保存测试失败 ✗\n错误: ' + error.message
      })
      this.addLog('error', `数据保存测试失败: ${error.message}`)
    }
  },

  /**
   * 测试数据加载
   */
  async testDataLoad() {
    try {
      this.setData({ loading: true, loadingText: '测试数据加载...' })

      await this.dataManager.loadData()

      this.setData({
        loading: false,
        testResults: '数据加载测试通过 ✓\n加载时间: ' + new Date().toLocaleString()
      })

      this.addLog('info', '数据加载测试完成')
    } catch (error) {
      this.setData({
        loading: false,
        testResults: '数据加载测试失败 ✗\n错误: ' + error.message
      })
      this.addLog('error', `数据加载测试失败: ${error.message}`)
    }
  },

  /**
   * 测试数据压缩
   */
  async testCompression() {
    try {
      this.setData({ loading: true, loadingText: '测试数据压缩...' })

      const testData = { test: 'data', array: [1, 2, 3, 4, 5], nested: { a: 1, b: 2 } }
      const originalJson = JSON.stringify(testData)
      const originalSize = originalJson.length

      // 模拟存储管理器的压缩过程
      // 先保存测试数据，然后检查压缩效果
      const testKey = 'DEBUG_COMPRESSION_TEST'
      await this.storageManager.saveData(testData)

      // 获取压缩后的数据
      const compressedData = wx.getStorageSync(this.storageKey || 'USER_DATA')
      const compressedSize = compressedData ? compressedData.length : originalSize

      const compressionRatio = originalSize > 0 ?
        ((originalSize - compressedSize) / originalSize * 100).toFixed(2) : '0.00'

      this.setData({
        loading: false,
        testResults: `数据压缩测试通过 ✓
原始大小: ${originalSize} 字节
压缩后大小: ${compressedSize} 字节
压缩率: ${compressionRatio}%
测试时间: ${new Date().toLocaleString()}`
      })

      this.addLog('info', '数据压缩测试完成')
    } catch (error) {
      this.setData({
        loading: false,
        testResults: '数据压缩测试失败 ✗\n错误: ' + error.message
      })
      this.addLog('error', `数据压缩测试失败: ${error.message}`)
    }
  },

  /**
   * 测试云函数
   */
  async testCloudFunction() {
    try {
      this.setData({ loading: true, loadingText: '测试云函数...' })

      const result = await wx.cloud.callFunction({
        name: 'cloud-functions',
        data: {
          type: 'getOpenId'
        }
      })

      this.setData({
        loading: false,
        testResults: `云函数测试通过 ✓
OpenID: ${result.result.openid}
响应时间: ${new Date().toLocaleString()}`
      })

      this.addLog('info', '云函数测试完成')
    } catch (error) {
      this.setData({
        loading: false,
        testResults: '云函数测试失败 ✗\n错误: ' + error.message
      })
      this.addLog('error', `云函数测试失败: ${error.message}`)
    }
  },

  /**
   * 测试通知
   */
  testNotification() {
    wx.showToast({
      title: '测试通知',
      icon: 'success',
      duration: 2000
    })

    this.setData({
      testResults: '通知测试完成 ✓\n显示时间: ' + new Date().toLocaleString()
    })

    this.addLog('info', '通知测试完成')
  },

  /**
   * 性能测试
   */
  testPerformance() {
    const startTime = Date.now()

    // 执行一些计算密集型操作
    let result = 0
    for (let i = 0; i < 1000000; i++) {
      result += Math.random()
    }

    const endTime = Date.now()
    const duration = endTime - startTime

    this.setData({
      testResults: `性能测试完成 ✓
计算时间: ${duration}ms
计算结果: ${result.toFixed(2)}
测试时间: ${new Date().toLocaleString()}`
    })

    this.addLog('info', `性能测试完成，耗时 ${duration}ms`)
  },

  // ==================== 日志相关方法 ====================

  /**
   * 刷新日志
   */
  refreshLogs() {
    // 日志是实时更新的，这里只是给用户反馈
    wx.showToast({
      title: '日志已刷新',
      icon: 'success'
    })
    this.addLog('info', '日志已手动刷新')
  },

  /**
   * 清空日志
   */
  clearLogs() {
    wx.showModal({
      title: '清空日志',
      content: '确定要清空所有日志记录吗？',
      success: (res) => {
        if (res.confirm) {
          this.setData({ logs: [] })
          this.addLog('info', '日志已清空')
        }
      }
    })
  },

  /**
   * 导出日志
   */
  exportLogs() {
    try {
      const logs = this.data.logs
      const logText = logs.map(log => `[${log.time}] ${log.level.toUpperCase()}: ${log.message}`).join('\n')

      wx.setClipboardData({
        data: logText,
        success: () => {
          wx.showToast({
            title: '日志已复制到剪贴板',
            icon: 'success'
          })
          this.addLog('info', '日志已导出到剪贴板')
        }
      })
    } catch (error) {
      this.addLog('error', `导出日志失败: ${error.message}`)
    }
  },

  // ==================== 开发工具相关方法 ====================

  /**
   * 重置应用
   */
  resetApp() {
    wx.showModal({
      title: '重置应用',
      content: '这将清空所有数据并重启应用，确定继续吗？',
      success: (res) => {
        if (res.confirm) {
          this.doResetApp()
        }
      }
    })
  },

  /**
   * 执行重置应用
   */
  async doResetApp() {
    try {
      this.setData({ loading: true, loadingText: '重置应用中...' })

      // 清空所有存储
      wx.clearStorageSync()

      // 重新启动应用
      wx.reLaunch({
        url: '/pages/index/index'
      })
    } catch (error) {
      this.setData({ loading: false })
      this.addLog('error', `重置应用失败: ${error.message}`)
    }
  },

  /**
   * 模拟错误
   */
  simulateError() {
    wx.showActionSheet({
      itemList: ['JavaScript错误', '网络错误', '数据错误', '权限错误'],
      success: (res) => {
        switch (res.tapIndex) {
          case 0:
            this.simulateJSError()
            break
          case 1:
            this.simulateNetworkError()
            break
          case 2:
            this.simulateDataError()
            break
          case 3:
            this.simulatePermissionError()
            break
        }
      }
    })
  },

  /**
   * 模拟JavaScript错误
   */
  simulateJSError() {
    try {
      // 故意触发一个错误
      const obj = null
      obj.someProperty.someMethod()
    } catch (error) {
      this.addLog('error', `模拟JS错误: ${error.message}`)
      console.error('模拟的JavaScript错误:', error)
    }
  },

  /**
   * 模拟网络错误
   */
  async simulateNetworkError() {
    try {
      await wx.request({
        url: 'https://invalid-url-that-does-not-exist.com/api/test',
        method: 'GET'
      })
    } catch (error) {
      this.addLog('error', `模拟网络错误: ${error.message}`)
      console.error('模拟的网络错误:', error)
    }
  },

  /**
   * 模拟数据错误
   */
  simulateDataError() {
    try {
      const invalidData = '{"invalid": json}'
      JSON.parse(invalidData)
    } catch (error) {
      this.addLog('error', `模拟数据错误: ${error.message}`)
      console.error('模拟的数据错误:', error)
    }
  },

  /**
   * 模拟权限错误
   */
  simulatePermissionError() {
    wx.getLocation({
      type: 'wgs84',
      fail: (error) => {
        this.addLog('error', `模拟权限错误: ${error.message}`)
        console.error('模拟的权限错误:', error)
      }
    })
  },

  /**
   * 强制同步
   */
  async forceSync() {
    try {
      this.setData({ loading: true, loadingText: '强制同步中...' })

      if (this.syncManager && this.syncManager.manualSync) {
        await this.syncManager.manualSync()
        this.addLog('info', '强制同步完成')
      } else {
        this.addLog('warn', '同步管理器不可用')
      }

      this.setData({ loading: false })
    } catch (error) {
      this.setData({ loading: false })
      this.addLog('error', `强制同步失败: ${error.message}`)
    }
  },

  /**
   * 调试模式
   */
  debugMode() {
    const isDebugMode = wx.getStorageSync('debugMode') || false
    const newDebugMode = !isDebugMode

    wx.setStorageSync('debugMode', newDebugMode)

    wx.showToast({
      title: newDebugMode ? '调试模式已开启' : '调试模式已关闭',
      icon: 'success'
    })

    this.addLog('info', `调试模式${newDebugMode ? '开启' : '关闭'}`)
  },

  /**
   * 自定义命令输入
   */
  onCustomCommandInput(e) {
    this.setData({
      customCommand: e.detail.value
    })
  },

  /**
   * 执行自定义命令
   */
  executeCustomCommand() {
    const command = this.data.customCommand.trim()

    if (!command) {
      wx.showToast({
        title: '请输入命令',
        icon: 'none'
      })
      return
    }

    try {
      // 简单的命令解析
      if (command === 'clear') {
        this.clearLogs()
      } else if (command === 'refresh') {
        this.refreshAllData()
      } else if (command === 'export') {
        this.exportUserData()
      } else if (command.startsWith('log ')) {
        const message = command.substring(4)
        this.addLog('info', `自定义: ${message}`)
      } else {
        // 尝试作为JavaScript代码执行（危险，仅用于调试）
        const result = eval(command)
        this.addLog('info', `命令执行结果: ${result}`)
      }

      this.setData({ customCommand: '' })
    } catch (error) {
      this.addLog('error', `命令执行失败: ${error.message}`)
    }
  },

  // ==================== 错误监控增强相关方法 ====================

  /**
   * 初始化错误监控
   */
  initializeErrorMonitoring() {
    // 监听小程序错误
    if (wx.onError) {
      wx.onError((error) => {
        this.recordError('小程序错误', error)
      })
    }

    // 监听未处理的Promise拒绝
    if (wx.onUnhandledRejection) {
      wx.onUnhandledRejection((res) => {
        this.recordError('Promise拒绝', res.reason)
      })
    }

    // 监听内存不足警告
    if (wx.onMemoryWarning) {
      wx.onMemoryWarning((res) => {
        this.recordError('内存警告', `内存警告级别: ${res.level}`)
      })
    }

    this.addLog('info', '错误监控初始化完成')
  },

  /**
   * 记录错误
   */
  recordError(type, error) {
    const errorInfo = {
      type: type,
      message: String(error),
      timestamp: new Date().toLocaleString(),
      stack: error.stack || '无堆栈信息'
    }

    const errors = this.data.errorInfo.errors
    errors.unshift(errorInfo)

    // 限制错误记录数量
    if (errors.length > 50) {
      errors.splice(50)
    }

    this.setData({
      'errorInfo.errorCount': errors.length,
      'errorInfo.errors': errors
    })

    this.addLog('error', `${type}: ${errorInfo.message}`)
  },

  /**
   * 查看错误详情
   */
  viewErrorDetails(e) {
    try {
      const index = e.currentTarget.dataset.index
      const error = this.data.errorInfo.errors[index]
      if (!error) return

      const details = `错误类型: ${error.type}
发生时间: ${error.timestamp}
错误信息: ${error.message}

堆栈信息:
${error.stack}`

      wx.showModal({
        title: '错误详情',
        content: details,
        showCancel: false,
        confirmText: '知道了'
      })
    } catch (error) {
      this.addLog('error', `查看错误详情失败: ${error.message}`)
    }
  },

  /**
   * 清空错误记录
   */
  clearErrorRecords() {
    wx.showModal({
      title: '清空错误记录',
      content: '确定要清空所有错误记录吗？',
      success: (res) => {
        if (res.confirm) {
          this.setData({
            'errorInfo.errorCount': 0,
            'errorInfo.errors': []
          })
          this.addLog('info', '错误记录已清空')
        }
      }
    })
  },

  /**
   * 导出错误报告
   */
  exportErrorReport() {
    try {
      const errors = this.data.errorInfo.errors
      const report = {
        exportTime: new Date().toLocaleString(),
        totalErrors: errors.length,
        appInfo: this.data.appInfo,
        systemInfo: this.data.systemInfo,
        errors: errors
      }

      const reportText = JSON.stringify(report, null, 2)

      wx.setClipboardData({
        data: reportText,
        success: () => {
          wx.showToast({
            title: '错误报告已复制',
            icon: 'success'
          })
          this.addLog('info', '错误报告已导出到剪贴板')
        }
      })
    } catch (error) {
      this.addLog('error', `导出错误报告失败: ${error.message}`)
    }
  },

  /**
   * 触发测试错误
   */
  triggerTestError() {
    wx.showActionSheet({
      itemList: [
        '触发JavaScript错误',
        '触发Promise拒绝',
        '触发网络错误',
        '触发内存警告模拟'
      ],
      success: (res) => {
        switch (res.tapIndex) {
          case 0:
            // JavaScript错误
            setTimeout(() => {
              throw new Error('这是一个测试JavaScript错误')
            }, 100)
            break
          case 1:
            // Promise拒绝
            Promise.reject(new Error('这是一个测试Promise拒绝'))
            break
          case 2:
            // 网络错误
            wx.request({
              url: 'https://invalid-test-url-that-does-not-exist.com',
              fail: (error) => {
                this.recordError('网络错误', error.message)
              }
            })
            break
          case 3:
            // 模拟内存警告
            this.recordError('内存警告', '模拟内存警告 - 测试用')
            break
        }
      }
    })
  },

  // ==================== 数据压缩分析相关方法 ====================

  /**
   * 测试用户数据压缩
   */
  async testUserDataCompression() {
    try {
      this.setData({ loading: true, loadingText: '分析用户数据压缩...' })

      await this.dataManager.ensureLoaded()
      const userData = this.dataManager.getUserData()

      if (!userData || Object.keys(userData).length === 0) {
        this.setData({
          loading: false,
          testResults: '用户数据压缩测试失败 ✗\n错误: 用户数据为空，请先添加一些数据'
        })
        this.addLog('warn', '用户数据为空，无法进行压缩测试')
        return
      }

      const startTime = Date.now()

      // 获取原始数据
      const originalJson = JSON.stringify(userData)
      const originalSize = originalJson.length

      // 使用存储管理器的实际压缩方法
      const compressStartTime = Date.now()

      let compressedSize = originalSize
      let compressionTime = 0
      let decompressionTime = 0

      try {
        // 如果存储管理器有压缩方法，使用它
        if (this.storageManager && this.storageManager.compressData) {
          const compressedData = await this.storageManager.compressData(userData)
          compressedSize = JSON.stringify(compressedData).length
          compressionTime = Date.now() - compressStartTime

          // 测试解压缩
          const decompressStartTime = Date.now()
          await this.storageManager.decompressData(compressedData)
          decompressionTime = Date.now() - decompressStartTime
        } else {
          // 使用简单的JSON字符串长度比较
          const testKey = 'COMPRESSION_TEST_' + Date.now()

          // 保存原始数据
          wx.setStorageSync(testKey, userData)

          // 获取存储后的数据大小（微信会自动进行一些优化）
          const storedData = wx.getStorageSync(testKey)
          compressedSize = JSON.stringify(storedData).length
          compressionTime = Date.now() - compressStartTime

          // 测试读取时间
          const decompressStartTime = Date.now()
          wx.getStorageSync(testKey)
          decompressionTime = Date.now() - decompressStartTime

          // 清理测试数据
          wx.removeStorageSync(testKey)
        }
      } catch (error) {
        // 如果压缩失败，使用原始大小
        compressionTime = Date.now() - compressStartTime
        this.addLog('warn', `压缩过程出现问题: ${error.message}`)
      }

      const totalTime = Date.now() - startTime

      // 计算压缩率
      const compressionRatio = originalSize > 0 ?
        ((originalSize - compressedSize) / originalSize * 100).toFixed(2) : '0.00'

      // 计算压缩效率
      const compressionEfficiency = compressionTime > 0 ?
        (originalSize / compressionTime).toFixed(2) : '0'

      const testResult = {
        timestamp: new Date().toLocaleString(),
        originalSize: originalSize,
        compressedSize: compressedSize,
        compressionRatio: parseFloat(compressionRatio),
        compressionTime: compressionTime,
        decompressionTime: decompressionTime,
        totalTime: totalTime,
        compressionEfficiency: parseFloat(compressionEfficiency),
        dataType: 'userData',
        success: true
      }

      // 保存测试结果
      const history = this.data.compressionInfo.history
      history.unshift(testResult)
      if (history.length > 10) {
        history.splice(10) // 只保留最近10次测试
      }

      this.setData({
        'compressionInfo.lastTest': testResult,
        'compressionInfo.history': history,
        loading: false
      })

      // 显示结果
      const resultText = `用户数据压缩测试完成 ✓

原始大小: ${this.formatBytes(originalSize)}
压缩后大小: ${this.formatBytes(compressedSize)}
压缩率: ${compressionRatio}%
节省空间: ${this.formatBytes(originalSize - compressedSize)}

性能指标:
压缩耗时: ${compressionTime}ms
解压耗时: ${decompressionTime}ms
总耗时: ${totalTime}ms
压缩效率: ${compressionEfficiency} 字节/ms`

      this.setData({
        testResults: resultText
      })

      this.addLog('info', `用户数据压缩测试完成，压缩率: ${compressionRatio}%`)

    } catch (error) {
      this.setData({
        loading: false,
        testResults: '用户数据压缩测试失败 ✗\n错误: ' + error.message
      })
      this.addLog('error', `用户数据压缩测试失败: ${error.message}`)
    }
  },

  /**
   * 测试自定义数据压缩
   */
  async testCustomDataCompression() {
    try {
      this.setData({ loading: true, loadingText: '测试自定义数据压缩...' })

      // 生成不同类型的测试数据
      const testDataSets = [
        {
          name: '简单对象',
          data: { name: 'test', value: 123, flag: true, timestamp: Date.now() }
        },
        {
          name: '数组数据',
          data: Array.from({length: 100}, (_, i) => ({
            id: i,
            name: `item_${i}`,
            value: Math.random(),
            category: i % 5,
            active: i % 2 === 0
          }))
        },
        {
          name: '重复字符串',
          data: {
            content: 'Hello World! '.repeat(100),
            metadata: 'Repeated content test',
            count: 100
          }
        },
        {
          name: '复杂嵌套',
          data: {
            users: Array.from({length: 50}, (_, i) => ({
              id: i,
              profile: {
                name: `User ${i}`,
                email: `user${i}@example.com`,
                settings: {
                  theme: i % 2 === 0 ? 'dark' : 'light',
                  notifications: true,
                  language: 'zh-CN'
                }
              },
              posts: Array.from({length: 3}, (_, j) => ({
                id: j,
                title: `Post ${j} by User ${i}`,
                content: `This is post content ${j}`,
                likes: Math.floor(Math.random() * 100)
              }))
            }))
          }
        }
      ]

      const results = []

      for (const testSet of testDataSets) {
        const startTime = Date.now()

        const originalJson = JSON.stringify(testSet.data)
        const originalSize = originalJson.length

        // 压缩测试
        const compressStartTime = Date.now()
        const testKey = 'COMPRESSION_TEST_' + Date.now() + '_' + Math.random()

        try {
          // 保存数据
          wx.setStorageSync(testKey, testSet.data)

          // 获取存储后的数据
          const storedData = wx.getStorageSync(testKey)
          const compressedSize = JSON.stringify(storedData).length

          const compressEndTime = Date.now()

          // 清理
          wx.removeStorageSync(testKey)

          const totalTime = Date.now() - startTime
          const compressionTime = compressEndTime - compressStartTime
          const compressionRatio = originalSize > 0 ?
            ((originalSize - compressedSize) / originalSize * 100).toFixed(2) : '0.00'

          results.push({
            name: testSet.name,
            originalSize: originalSize,
            compressedSize: compressedSize,
            compressionRatio: parseFloat(compressionRatio),
            compressionTime: compressionTime,
            totalTime: totalTime,
            success: true
          })
        } catch (error) {
          results.push({
            name: testSet.name,
            originalSize: originalSize,
            compressedSize: originalSize,
            compressionRatio: 0,
            compressionTime: 0,
            totalTime: Date.now() - startTime,
            success: false,
            error: error.message
          })
        }
      }

      this.setData({ loading: false })

      // 显示结果
      let resultText = '自定义数据压缩测试完成 ✓\n\n'
      results.forEach(result => {
        if (result.success) {
          resultText += `${result.name}:
  原始: ${this.formatBytes(result.originalSize)}
  压缩: ${this.formatBytes(result.compressedSize)}
  压缩率: ${result.compressionRatio}%
  耗时: ${result.compressionTime}ms

`
        } else {
          resultText += `${result.name}: 测试失败 - ${result.error}\n\n`
        }
      })

      this.setData({
        testResults: resultText
      })

      this.addLog('info', '自定义数据压缩测试完成')

    } catch (error) {
      this.setData({
        loading: false,
        testResults: '自定义数据压缩测试失败 ✗\n错误: ' + error.message
      })
      this.addLog('error', `自定义数据压缩测试失败: ${error.message}`)
    }
  },

  /**
   * 压缩性能基准测试
   */
  async compressionBenchmark() {
    try {
      this.setData({ loading: true, loadingText: '执行压缩性能基准测试...' })

      const testSizes = [1, 10, 100, 500, 1000] // KB
      const results = []

      for (const sizeKB of testSizes) {
        // 生成指定大小的测试数据
        const targetSize = sizeKB * 1024
        const testData = this.generateTestData(targetSize)

        const iterations = 5 // 每个大小测试5次取平均值
        let totalCompressionTime = 0
        let totalOriginalSize = 0
        let totalCompressedSize = 0

        for (let i = 0; i < iterations; i++) {
          const startTime = Date.now()

          const originalJson = JSON.stringify(testData)
          const originalSize = originalJson.length

          const testKey = 'BENCHMARK_TEST_' + Date.now() + '_' + i
          wx.setStorageSync(testKey, originalJson)
          const compressedData = wx.getStorageSync(testKey)
          const compressedSize = compressedData ? compressedData.length : originalSize

          const endTime = Date.now()

          totalCompressionTime += (endTime - startTime)
          totalOriginalSize += originalSize
          totalCompressedSize += compressedSize

          wx.removeStorageSync(testKey)
        }

        const avgCompressionTime = totalCompressionTime / iterations
        const avgOriginalSize = totalOriginalSize / iterations
        const avgCompressedSize = totalCompressedSize / iterations
        const avgCompressionRatio = avgOriginalSize > 0 ?
          ((avgOriginalSize - avgCompressedSize) / avgOriginalSize * 100).toFixed(2) : '0.00'

        results.push({
          targetSize: sizeKB,
          avgOriginalSize: avgOriginalSize,
          avgCompressedSize: avgCompressedSize,
          avgCompressionRatio: parseFloat(avgCompressionRatio),
          avgCompressionTime: avgCompressionTime,
          throughput: avgOriginalSize / avgCompressionTime // 字节/毫秒
        })
      }

      this.setData({ loading: false })

      // 显示结果
      let resultText = '压缩性能基准测试完成 ✓\n\n'
      resultText += '数据大小 | 压缩率 | 耗时 | 吞吐量\n'
      resultText += '-------|------|-----|-------\n'

      results.forEach(result => {
        resultText += `${result.targetSize}KB | ${result.avgCompressionRatio}% | ${result.avgCompressionTime.toFixed(1)}ms | ${result.throughput.toFixed(0)} B/ms\n`
      })

      this.setData({
        testResults: resultText
      })

      this.addLog('info', '压缩性能基准测试完成')

    } catch (error) {
      this.setData({
        loading: false,
        testResults: '压缩性能基准测试失败 ✗\n错误: ' + error.message
      })
      this.addLog('error', `压缩性能基准测试失败: ${error.message}`)
    }
  },

  /**
   * 生成指定大小的测试数据
   */
  generateTestData(targetSize) {
    const baseString = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789'
    const data = {
      id: Math.random().toString(36),
      timestamp: Date.now(),
      content: '',
      metadata: {
        version: '1.0.0',
        type: 'test_data',
        generated: new Date().toISOString()
      }
    }

    // 填充内容直到达到目标大小
    while (JSON.stringify(data).length < targetSize) {
      data.content += baseString
    }

    return data
  },

  /**
   * 格式化字节大小
   */
  formatBytes(bytes) {
    if (bytes === 0) return '0 B'

    const k = 1024
    const sizes = ['B', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))

    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  },

  /**
   * 查看压缩历史
   */
  viewCompressionHistory() {
    const history = this.data.compressionInfo.history

    if (history.length === 0) {
      wx.showToast({
        title: '暂无压缩历史',
        icon: 'none'
      })
      return
    }

    let historyText = '压缩测试历史:\n\n'
    history.forEach((test, index) => {
      historyText += `${index + 1}. ${test.timestamp}
类型: ${test.dataType}
压缩率: ${test.compressionRatio}%
耗时: ${test.compressionTime}ms
${test.success ? '✓' : '✗'}

`
    })

    wx.showModal({
      title: '压缩历史记录',
      content: historyText,
      showCancel: false,
      confirmText: '知道了'
    })
  },

  // ==================== 性能分析相关方法 ====================

  /**
   * 执行性能分析测试
   */
  async performanceAnalysis() {
    try {
      this.setData({ loading: true, loadingText: '执行性能分析...' })

      const results = {}

      // 1. CPU性能测试
      results.cpu = await this.testCPUPerformance()

      // 2. 内存使用分析
      results.memory = this.analyzeMemoryUsage()

      // 3. 存储I/O性能测试
      results.storage = await this.testStoragePerformance()

      // 4. 页面渲染性能测试
      results.rendering = await this.testRenderingPerformance()

      const testResult = {
        timestamp: new Date().toLocaleString(),
        results: results,
        score: this.calculatePerformanceScore(results)
      }

      const tests = this.data.performanceInfo.tests
      tests.unshift(testResult)
      if (tests.length > 5) {
        tests.splice(5) // 只保留最近5次测试
      }

      this.setData({
        'performanceInfo.tests': tests,
        'performanceInfo.currentTest': testResult,
        loading: false
      })

      // 显示结果
      const resultText = `性能分析完成 ✓

综合评分: ${testResult.score}/100

CPU性能: ${results.cpu.score}/100
- 计算速度: ${results.cpu.computeSpeed.toFixed(2)} ops/ms
- 数组处理: ${results.cpu.arrayProcessing.toFixed(2)} ms

内存使用: ${results.memory.score}/100
- 已用内存: ${this.formatBytes(results.memory.usedMemory)}
- 内存效率: ${results.memory.efficiency}%

存储I/O: ${results.storage.score}/100
- 写入速度: ${results.storage.writeSpeed.toFixed(2)} KB/s
- 读取速度: ${results.storage.readSpeed.toFixed(2)} KB/s

渲染性能: ${results.rendering.score}/100
- 帧率: ${results.rendering.fps} FPS
- 渲染延迟: ${results.rendering.latency}ms`

      this.setData({
        testResults: resultText
      })

      this.addLog('info', `性能分析完成，综合评分: ${testResult.score}`)

    } catch (error) {
      this.setData({
        loading: false,
        testResults: '性能分析失败 ✗\n错误: ' + error.message
      })
      this.addLog('error', `性能分析失败: ${error.message}`)
    }
  },

  /**
   * CPU性能测试
   */
  async testCPUPerformance() {
    const startTime = Date.now()

    // 计算密集型任务
    let result = 0
    const iterations = 100000
    for (let i = 0; i < iterations; i++) {
      result += Math.sqrt(i) * Math.sin(i) * Math.cos(i)
    }

    const computeTime = Date.now() - startTime
    const computeSpeed = iterations / computeTime

    // 数组处理测试
    const arrayStartTime = Date.now()
    const testArray = Array.from({length: 10000}, (_, i) => i)
    const processedArray = testArray
      .filter(x => x % 2 === 0)
      .map(x => x * 2)
      .reduce((sum, x) => sum + x, 0)
    const arrayTime = Date.now() - arrayStartTime

    const score = Math.min(100, Math.max(0, 100 - computeTime / 10))

    return {
      computeSpeed: computeSpeed,
      arrayProcessing: arrayTime,
      score: Math.round(score)
    }
  },

  /**
   * 内存使用分析
   */
  analyzeMemoryUsage() {
    let usedMemory = 0
    let efficiency = 100

    if (wx.getPerformance) {
      const performance = wx.getPerformance()
      if (performance.memory) {
        usedMemory = performance.memory.usedJSHeapSize
        const totalMemory = performance.memory.totalJSHeapSize
        efficiency = totalMemory > 0 ? Math.round((1 - usedMemory / totalMemory) * 100) : 100
      }
    }

    const score = Math.min(100, efficiency)

    return {
      usedMemory: usedMemory,
      efficiency: efficiency,
      score: score
    }
  },

  /**
   * 存储I/O性能测试
   */
  async testStoragePerformance() {
    const testData = 'x'.repeat(10240) // 10KB测试数据
    const testKey = 'PERF_TEST_' + Date.now()

    // 写入测试
    const writeStartTime = Date.now()
    for (let i = 0; i < 10; i++) {
      wx.setStorageSync(testKey + '_' + i, testData)
    }
    const writeTime = Date.now() - writeStartTime
    const writeSpeed = (testData.length * 10) / writeTime // bytes/ms -> KB/s

    // 读取测试
    const readStartTime = Date.now()
    for (let i = 0; i < 10; i++) {
      wx.getStorageSync(testKey + '_' + i)
    }
    const readTime = Date.now() - readStartTime
    const readSpeed = (testData.length * 10) / readTime

    // 清理测试数据
    for (let i = 0; i < 10; i++) {
      wx.removeStorageSync(testKey + '_' + i)
    }

    const avgSpeed = (writeSpeed + readSpeed) / 2
    const score = Math.min(100, Math.max(0, avgSpeed / 10))

    return {
      writeSpeed: writeSpeed,
      readSpeed: readSpeed,
      score: Math.round(score)
    }
  },

  /**
   * 渲染性能测试
   */
  async testRenderingPerformance() {
    const startTime = Date.now()

    // 模拟DOM操作
    const testData = Array.from({length: 100}, (_, i) => ({ id: i, value: Math.random() }))

    // 模拟数据更新
    this.setData({
      testRenderData: testData
    })

    await new Promise(resolve => setTimeout(resolve, 100))

    const renderTime = Date.now() - startTime
    const fps = Math.round(1000 / renderTime * 60) // 估算FPS
    const latency = renderTime

    const score = Math.min(100, Math.max(0, 100 - latency / 5))

    return {
      fps: Math.min(60, fps),
      latency: latency,
      score: Math.round(score)
    }
  },

  /**
   * 计算综合性能评分
   */
  calculatePerformanceScore(results) {
    const weights = {
      cpu: 0.3,
      memory: 0.25,
      storage: 0.25,
      rendering: 0.2
    }

    const totalScore =
      results.cpu.score * weights.cpu +
      results.memory.score * weights.memory +
      results.storage.score * weights.storage +
      results.rendering.score * weights.rendering

    return Math.round(totalScore)
  },

  /**
   * 查看性能历史
   */
  viewPerformanceHistory() {
    const tests = this.data.performanceInfo.tests

    if (tests.length === 0) {
      wx.showToast({
        title: '暂无性能测试历史',
        icon: 'none'
      })
      return
    }

    let historyText = '性能测试历史:\n\n'
    tests.forEach((test, index) => {
      historyText += `${index + 1}. ${test.timestamp}
综合评分: ${test.score}/100
CPU: ${test.results.cpu.score}/100
内存: ${test.results.memory.score}/100
存储: ${test.results.storage.score}/100
渲染: ${test.results.rendering.score}/100

`
    })

    wx.showModal({
      title: '性能测试历史',
      content: historyText,
      showCancel: false,
      confirmText: '知道了'
    })
  },

  // ==================== 网络诊断相关方法 ====================

  /**
   * 网络诊断测试
   */
  async networkDiagnostics() {
    try {
      this.setData({ loading: true, loadingText: '执行网络诊断...' })

      const results = {}

      // 1. 网络类型检测
      results.networkType = await this.detectNetworkType()

      // 2. 网络延迟测试
      results.latency = await this.testNetworkLatency()

      // 3. 网络速度测试
      results.speed = await this.testNetworkSpeed()

      // 4. DNS解析测试
      results.dns = await this.testDNSResolution()

      // 5. 连接稳定性测试
      results.stability = await this.testConnectionStability()

      this.setData({
        'networkInfo.status': results.networkType.type,
        'networkInfo.speed': results.speed.downloadSpeed,
        'networkInfo.latency': results.latency.average,
        loading: false
      })

      // 显示结果
      const resultText = `网络诊断完成 ✓

网络类型: ${results.networkType.type}
网络状态: ${results.networkType.isConnected ? '已连接' : '未连接'}

延迟测试:
- 平均延迟: ${results.latency.average}ms
- 最小延迟: ${results.latency.min}ms
- 最大延迟: ${results.latency.max}ms
- 丢包率: ${results.latency.packetLoss}%

速度测试:
- 下载速度: ${results.speed.downloadSpeed.toFixed(2)} KB/s
- 上传速度: ${results.speed.uploadSpeed.toFixed(2)} KB/s

DNS解析:
- 解析时间: ${results.dns.resolveTime}ms
- 解析状态: ${results.dns.success ? '成功' : '失败'}

连接稳定性:
- 稳定性评分: ${results.stability.score}/100
- 连接成功率: ${results.stability.successRate}%`

      this.setData({
        testResults: resultText
      })

      this.addLog('info', `网络诊断完成，延迟: ${results.latency.average}ms`)

    } catch (error) {
      this.setData({
        loading: false,
        testResults: '网络诊断失败 ✗\n错误: ' + error.message
      })
      this.addLog('error', `网络诊断失败: ${error.message}`)
    }
  },

  /**
   * 检测网络类型
   */
  detectNetworkType() {
    return new Promise((resolve) => {
      wx.getNetworkType({
        success: (res) => {
          resolve({
            type: res.networkType,
            isConnected: res.networkType !== 'none'
          })
        },
        fail: () => {
          resolve({
            type: 'unknown',
            isConnected: false
          })
        }
      })
    })
  },

  /**
   * 测试网络延迟
   */
  async testNetworkLatency() {
    const testUrls = [
      'https://www.baidu.com',
      'https://www.qq.com',
      'https://www.163.com'
    ]

    const latencies = []
    let successCount = 0

    for (const url of testUrls) {
      try {
        const startTime = Date.now()

        await new Promise((resolve, reject) => {
          wx.request({
            url: url,
            method: 'HEAD',
            timeout: 5000,
            success: () => {
              const latency = Date.now() - startTime
              latencies.push(latency)
              successCount++
              resolve()
            },
            fail: reject
          })
        })
      } catch (error) {
        // 请求失败，记录为超时
        latencies.push(5000)
      }
    }

    const average = latencies.length > 0 ?
      Math.round(latencies.reduce((sum, lat) => sum + lat, 0) / latencies.length) : 0
    const min = latencies.length > 0 ? Math.min(...latencies) : 0
    const max = latencies.length > 0 ? Math.max(...latencies) : 0
    const packetLoss = Math.round((1 - successCount / testUrls.length) * 100)

    return {
      average: average,
      min: min,
      max: max,
      packetLoss: packetLoss
    }
  },

  /**
   * 测试网络速度
   */
  async testNetworkSpeed() {
    try {
      // 下载速度测试（使用小文件）
      const downloadStartTime = Date.now()
      const testData = 'x'.repeat(10240) // 10KB测试数据

      // 模拟下载测试
      await new Promise((resolve, reject) => {
        wx.request({
          url: 'https://httpbin.org/bytes/10240',
          timeout: 10000,
          success: () => {
            resolve()
          },
          fail: () => {
            // 如果外部请求失败，使用本地测试
            resolve()
          }
        })
      })

      const downloadTime = Date.now() - downloadStartTime
      const downloadSpeed = downloadTime > 0 ? (10240 / downloadTime) : 0

      // 上传速度测试（模拟）
      const uploadStartTime = Date.now()

      await new Promise((resolve) => {
        // 模拟上传延迟
        setTimeout(resolve, Math.random() * 1000 + 500)
      })

      const uploadTime = Date.now() - uploadStartTime
      const uploadSpeed = uploadTime > 0 ? (10240 / uploadTime) : 0

      return {
        downloadSpeed: downloadSpeed,
        uploadSpeed: uploadSpeed
      }
    } catch (error) {
      return {
        downloadSpeed: 0,
        uploadSpeed: 0
      }
    }
  },

  /**
   * 测试DNS解析
   */
  async testDNSResolution() {
    try {
      const startTime = Date.now()

      await new Promise((resolve, reject) => {
        wx.request({
          url: 'https://www.baidu.com',
          method: 'HEAD',
          timeout: 3000,
          success: resolve,
          fail: reject
        })
      })

      const resolveTime = Date.now() - startTime

      return {
        resolveTime: resolveTime,
        success: true
      }
    } catch (error) {
      return {
        resolveTime: 0,
        success: false
      }
    }
  },

  /**
   * 测试连接稳定性
   */
  async testConnectionStability() {
    const testCount = 5
    let successCount = 0

    for (let i = 0; i < testCount; i++) {
      try {
        await new Promise((resolve, reject) => {
          wx.request({
            url: 'https://httpbin.org/status/200',
            timeout: 3000,
            success: () => {
              successCount++
              resolve()
            },
            fail: reject
          })
        })
      } catch (error) {
        // 连接失败
      }

      // 间隔500ms
      await new Promise(resolve => setTimeout(resolve, 500))
    }

    const successRate = Math.round((successCount / testCount) * 100)
    const score = successRate

    return {
      successRate: successRate,
      score: score
    }
  },

  /**
   * 快速网络检查
   */
  async quickNetworkCheck() {
    try {
      const startTime = Date.now()

      const networkType = await this.detectNetworkType()

      if (!networkType.isConnected) {
        wx.showToast({
          title: '网络未连接',
          icon: 'error'
        })
        return
      }

      // 简单的连通性测试
      await new Promise((resolve, reject) => {
        wx.request({
          url: 'https://www.baidu.com',
          method: 'HEAD',
          timeout: 3000,
          success: resolve,
          fail: reject
        })
      })

      const latency = Date.now() - startTime

      wx.showToast({
        title: `网络正常 ${latency}ms`,
        icon: 'success'
      })

      this.addLog('info', `快速网络检查完成，延迟: ${latency}ms`)

    } catch (error) {
      wx.showToast({
        title: '网络连接异常',
        icon: 'error'
      })
      this.addLog('error', `快速网络检查失败: ${error.message}`)
    }
  },

  // ==================== 内存监控相关方法 ====================

  /**
   * 开始内存监控
   */
  startMemoryMonitoring() {
    try {
      // 清除之前的监控
      if (this.memoryMonitorTimer) {
        clearInterval(this.memoryMonitorTimer)
      }

      // 开始新的监控
      this.memoryMonitorTimer = setInterval(() => {
        this.collectMemoryInfo()
      }, 2000) // 每2秒收集一次

      this.setData({
        'memoryInfo.monitoring': true
      })

      wx.showToast({
        title: '内存监控已开启',
        icon: 'success'
      })

      this.addLog('info', '内存监控已开启')
    } catch (error) {
      this.addLog('error', `开启内存监控失败: ${error.message}`)
    }
  },

  /**
   * 停止内存监控
   */
  stopMemoryMonitoring() {
    try {
      if (this.memoryMonitorTimer) {
        clearInterval(this.memoryMonitorTimer)
        this.memoryMonitorTimer = null
      }

      this.setData({
        'memoryInfo.monitoring': false
      })

      wx.showToast({
        title: '内存监控已停止',
        icon: 'success'
      })

      this.addLog('info', '内存监控已停止')
    } catch (error) {
      this.addLog('error', `停止内存监控失败: ${error.message}`)
    }
  },

  /**
   * 收集内存信息
   */
  collectMemoryInfo() {
    try {
      let memoryData = {
        timestamp: Date.now(),
        timeString: new Date().toLocaleTimeString(),
        usedJSHeapSize: 0,
        totalJSHeapSize: 0,
        jsHeapSizeLimit: 0,
        usagePercent: 0
      }

      if (wx.getPerformance) {
        const performance = wx.getPerformance()
        if (performance.memory) {
          const memory = performance.memory
          memoryData.usedJSHeapSize = memory.usedJSHeapSize
          memoryData.totalJSHeapSize = memory.totalJSHeapSize
          memoryData.jsHeapSizeLimit = memory.jsHeapSizeLimit

          if (memory.totalJSHeapSize > 0) {
            memoryData.usagePercent = Math.round((memory.usedJSHeapSize / memory.totalJSHeapSize) * 100)
          }
        }
      }

      // 更新当前内存信息
      this.setData({
        'memoryInfo.current': memoryData
      })

      // 添加到历史记录
      const history = this.data.memoryInfo.history
      history.push(memoryData)

      // 限制历史记录数量
      if (history.length > 50) {
        history.shift()
      }

      this.setData({
        'memoryInfo.history': history
      })

      // 检查内存警告
      this.checkMemoryAlerts(memoryData)

    } catch (error) {
      this.addLog('error', `收集内存信息失败: ${error.message}`)
    }
  },

  /**
   * 检查内存警告
   */
  checkMemoryAlerts(memoryData) {
    const alerts = []

    // 内存使用率过高警告
    if (memoryData.usagePercent > 80) {
      alerts.push({
        level: 'error',
        message: `内存使用率过高: ${memoryData.usagePercent}%`,
        timestamp: memoryData.timeString
      })
    } else if (memoryData.usagePercent > 60) {
      alerts.push({
        level: 'warning',
        message: `内存使用率较高: ${memoryData.usagePercent}%`,
        timestamp: memoryData.timeString
      })
    }

    // 内存增长过快警告
    const history = this.data.memoryInfo.history
    if (history.length >= 5) {
      const recent = history.slice(-5)
      const growth = recent[recent.length - 1].usedJSHeapSize - recent[0].usedJSHeapSize
      const growthRate = growth / (5 * 2) // 每秒增长量

      if (growthRate > 1024 * 1024) { // 每秒增长超过1MB
        alerts.push({
          level: 'warning',
          message: `内存增长过快: ${this.formatBytes(growthRate)}/s`,
          timestamp: memoryData.timeString
        })
      }
    }

    if (alerts.length > 0) {
      const allAlerts = this.data.memoryInfo.alerts
      allAlerts.push(...alerts)

      // 限制警告数量
      if (allAlerts.length > 20) {
        allAlerts.splice(0, allAlerts.length - 20)
      }

      this.setData({
        'memoryInfo.alerts': allAlerts
      })

      // 记录到日志
      alerts.forEach(alert => {
        this.addLog(alert.level === 'error' ? 'error' : 'warn', alert.message)
      })
    }
  },

  /**
   * 内存分析报告
   */
  generateMemoryReport() {
    try {
      const current = this.data.memoryInfo.current
      const history = this.data.memoryInfo.history
      const alerts = this.data.memoryInfo.alerts

      if (!current.timestamp) {
        wx.showToast({
          title: '请先开启内存监控',
          icon: 'none'
        })
        return
      }

      // 计算统计信息
      let maxUsage = 0
      let minUsage = Infinity
      let avgUsage = 0
      let totalGrowth = 0

      if (history.length > 0) {
        const usages = history.map(h => h.usedJSHeapSize)
        maxUsage = Math.max(...usages)
        minUsage = Math.min(...usages)
        avgUsage = usages.reduce((sum, usage) => sum + usage, 0) / usages.length
        totalGrowth = usages[usages.length - 1] - usages[0]
      }

      const reportText = `内存分析报告 📊

当前状态:
- 已用内存: ${this.formatBytes(current.usedJSHeapSize)}
- 总内存: ${this.formatBytes(current.totalJSHeapSize)}
- 内存限制: ${this.formatBytes(current.jsHeapSizeLimit)}
- 使用率: ${current.usagePercent}%

历史统计 (${history.length}个采样点):
- 最大使用: ${this.formatBytes(maxUsage)}
- 最小使用: ${this.formatBytes(minUsage)}
- 平均使用: ${this.formatBytes(avgUsage)}
- 总增长: ${this.formatBytes(totalGrowth)}

警告信息:
- 警告数量: ${alerts.length}个
- 最近警告: ${alerts.length > 0 ? alerts[alerts.length - 1].message : '无'}

监控时长: ${Math.round((Date.now() - (history[0]?.timestamp || Date.now())) / 1000)}秒`

      this.setData({
        testResults: reportText
      })

      this.addLog('info', '内存分析报告已生成')

    } catch (error) {
      this.addLog('error', `生成内存报告失败: ${error.message}`)
    }
  },

  /**
   * 清空内存历史
   */
  clearMemoryHistory() {
    wx.showModal({
      title: '清空内存历史',
      content: '确定要清空所有内存监控历史数据吗？',
      success: (res) => {
        if (res.confirm) {
          this.setData({
            'memoryInfo.history': [],
            'memoryInfo.alerts': []
          })
          this.addLog('info', '内存历史数据已清空')
        }
      }
    })
  },

  // ==================== 事件监听器调试相关方法 ====================

  /**
   * 开始事件监听
   */
  startEventListening() {
    try {
      // 监听页面事件
      this.originalOnShow = Page.prototype.onShow
      this.originalOnHide = Page.prototype.onHide
      this.originalOnLoad = Page.prototype.onLoad
      this.originalOnUnload = Page.prototype.onUnload

      const self = this

      Page.prototype.onShow = function() {
        self.recordEvent('Page', 'onShow', this.route)
        if (self.originalOnShow) {
          return self.originalOnShow.apply(this, arguments)
        }
      }

      Page.prototype.onHide = function() {
        self.recordEvent('Page', 'onHide', this.route)
        if (self.originalOnHide) {
          return self.originalOnHide.apply(this, arguments)
        }
      }

      Page.prototype.onLoad = function() {
        self.recordEvent('Page', 'onLoad', this.route)
        if (self.originalOnLoad) {
          return self.originalOnLoad.apply(this, arguments)
        }
      }

      Page.prototype.onUnload = function() {
        self.recordEvent('Page', 'onUnload', this.route)
        if (self.originalOnUnload) {
          return self.originalOnUnload.apply(this, arguments)
        }
      }

      // 监听应用事件
      if (wx.onAppShow) {
        wx.onAppShow(() => {
          self.recordEvent('App', 'onShow', 'application')
        })
      }

      if (wx.onAppHide) {
        wx.onAppHide(() => {
          self.recordEvent('App', 'onHide', 'application')
        })
      }

      this.setData({
        'eventInfo.listening': true
      })

      wx.showToast({
        title: '事件监听已开启',
        icon: 'success'
      })

      this.addLog('info', '事件监听已开启')
    } catch (error) {
      this.addLog('error', `开启事件监听失败: ${error.message}`)
    }
  },

  /**
   * 停止事件监听
   */
  stopEventListening() {
    try {
      // 恢复原始方法
      if (this.originalOnShow) {
        Page.prototype.onShow = this.originalOnShow
      }
      if (this.originalOnHide) {
        Page.prototype.onHide = this.originalOnHide
      }
      if (this.originalOnLoad) {
        Page.prototype.onLoad = this.originalOnLoad
      }
      if (this.originalOnUnload) {
        Page.prototype.onUnload = this.originalOnUnload
      }

      this.setData({
        'eventInfo.listening': false
      })

      wx.showToast({
        title: '事件监听已停止',
        icon: 'success'
      })

      this.addLog('info', '事件监听已停止')
    } catch (error) {
      this.addLog('error', `停止事件监听失败: ${error.message}`)
    }
  },

  /**
   * 记录事件
   */
  recordEvent(type, event, target) {
    try {
      const eventData = {
        type: type,
        event: event,
        target: target,
        timestamp: Date.now(),
        timeString: new Date().toLocaleTimeString()
      }

      const events = this.data.eventInfo.events
      events.unshift(eventData)

      // 限制事件记录数量
      if (events.length > 100) {
        events.splice(100)
      }

      // 更新统计
      const stats = this.data.eventInfo.stats
      const key = `${type}_${event}`
      stats[key] = (stats[key] || 0) + 1

      this.setData({
        'eventInfo.events': events,
        'eventInfo.stats': stats
      })

      this.addLog('info', `事件: ${type}.${event} - ${target}`)
    } catch (error) {
      console.error('记录事件失败:', error)
    }
  },

  /**
   * 查看事件统计
   */
  viewEventStats() {
    try {
      const stats = this.data.eventInfo.stats
      const events = this.data.eventInfo.events

      if (Object.keys(stats).length === 0) {
        wx.showToast({
          title: '暂无事件统计',
          icon: 'none'
        })
        return
      }

      let statsText = '事件统计报告 📈\n\n'

      // 按次数排序
      const sortedStats = Object.entries(stats).sort((a, b) => b[1] - a[1])

      statsText += '事件频次:\n'
      sortedStats.forEach(([event, count]) => {
        statsText += `${event}: ${count}次\n`
      })

      statsText += `\n总事件数: ${events.length}个`

      if (events.length > 0) {
        const firstEvent = events[events.length - 1]
        const lastEvent = events[0]
        const duration = lastEvent.timestamp - firstEvent.timestamp
        statsText += `\n监听时长: ${Math.round(duration / 1000)}秒`
      }

      this.setData({
        testResults: statsText
      })

      this.addLog('info', '事件统计报告已生成')
    } catch (error) {
      this.addLog('error', `查看事件统计失败: ${error.message}`)
    }
  },

  /**
   * 清空事件记录
   */
  clearEventHistory() {
    wx.showModal({
      title: '清空事件记录',
      content: '确定要清空所有事件记录和统计吗？',
      success: (res) => {
        if (res.confirm) {
          this.setData({
            'eventInfo.events': [],
            'eventInfo.stats': {}
          })
          this.addLog('info', '事件记录已清空')
        }
      }
    })
  },

  /**
   * 触发测试事件
   */
  triggerTestEvents() {
    try {
      // 模拟各种事件
      this.recordEvent('Test', 'click', 'button')
      this.recordEvent('Test', 'scroll', 'page')
      this.recordEvent('Test', 'input', 'textfield')
      this.recordEvent('Test', 'change', 'picker')
      this.recordEvent('Test', 'submit', 'form')

      wx.showToast({
        title: '测试事件已触发',
        icon: 'success'
      })

      this.addLog('info', '测试事件已触发')
    } catch (error) {
      this.addLog('error', `触发测试事件失败: ${error.message}`)
    }
  },

  // ==================== 文件系统调试相关方法 ====================

  /**
   * 扫描文件系统
   */
  async scanFileSystem() {
    try {
      this.setData({ loading: true, loadingText: '扫描文件系统...' })

      const fileSystemManager = wx.getFileSystemManager()
      const files = []
      let totalSize = 0

      // 扫描用户目录
      await this.scanDirectory(fileSystemManager, wx.env.USER_DATA_PATH, files, '')

      // 计算总大小
      totalSize = files.reduce((sum, file) => sum + (file.size || 0), 0)

      this.setData({
        'fileSystemInfo.files': files,
        'fileSystemInfo.totalSize': totalSize,
        'fileSystemInfo.fileCount': files.length,
        loading: false
      })

      const resultText = `文件系统扫描完成 ✓

文件数量: ${files.length}个
总大小: ${this.formatBytes(totalSize)}

文件类型分布:
${this.getFileTypeDistribution(files)}

最大文件: ${this.getLargestFile(files)}`

      this.setData({
        testResults: resultText
      })

      this.addLog('info', `文件系统扫描完成，发现${files.length}个文件`)

    } catch (error) {
      this.setData({
        loading: false,
        testResults: '文件系统扫描失败 ✗\n错误: ' + error.message
      })
      this.addLog('error', `文件系统扫描失败: ${error.message}`)
    }
  },

  /**
   * 扫描目录
   */
  async scanDirectory(fsm, dirPath, files, relativePath) {
    try {
      const dirContents = fsm.readdirSync(dirPath)

      for (const item of dirContents) {
        const fullPath = `${dirPath}/${item}`
        const relativeItemPath = relativePath ? `${relativePath}/${item}` : item

        try {
          const stats = fsm.statSync(fullPath)

          if (stats.isFile()) {
            files.push({
              name: item,
              path: relativeItemPath,
              fullPath: fullPath,
              size: stats.size,
              modifyTime: stats.lastModifiedTime,
              type: this.getFileType(item)
            })
          } else if (stats.isDirectory() && files.length < 1000) {
            // 递归扫描子目录，但限制文件数量
            await this.scanDirectory(fsm, fullPath, files, relativeItemPath)
          }
        } catch (itemError) {
          // 跳过无法访问的文件
          console.warn('无法访问文件:', fullPath, itemError)
        }
      }
    } catch (error) {
      console.warn('无法扫描目录:', dirPath, error)
    }
  },

  /**
   * 获取文件类型
   */
  getFileType(fileName) {
    const ext = fileName.split('.').pop().toLowerCase()
    const typeMap = {
      'json': 'JSON',
      'txt': 'Text',
      'log': 'Log',
      'jpg': 'Image',
      'jpeg': 'Image',
      'png': 'Image',
      'gif': 'Image',
      'mp3': 'Audio',
      'mp4': 'Video',
      'pdf': 'PDF',
      'zip': 'Archive'
    }
    return typeMap[ext] || 'Other'
  },

  /**
   * 获取文件类型分布
   */
  getFileTypeDistribution(files) {
    const distribution = {}
    files.forEach(file => {
      distribution[file.type] = (distribution[file.type] || 0) + 1
    })

    return Object.entries(distribution)
      .sort((a, b) => b[1] - a[1])
      .map(([type, count]) => `${type}: ${count}个`)
      .join('\n')
  },

  /**
   * 获取最大文件
   */
  getLargestFile(files) {
    if (files.length === 0) return '无'

    const largest = files.reduce((max, file) =>
      (file.size || 0) > (max.size || 0) ? file : max
    )

    return `${largest.name} (${this.formatBytes(largest.size || 0)})`
  },

  /**
   * 清理临时文件
   */
  cleanTempFiles() {
    wx.showModal({
      title: '清理临时文件',
      content: '这将删除所有临时文件和缓存文件，确定继续吗？',
      success: (res) => {
        if (res.confirm) {
          this.performCleanup()
        }
      }
    })
  },

  /**
   * 执行清理
   */
  async performCleanup() {
    try {
      this.setData({ loading: true, loadingText: '清理临时文件...' })

      const fileSystemManager = wx.getFileSystemManager()
      const files = this.data.fileSystemInfo.files
      let cleanedCount = 0
      let cleanedSize = 0

      for (const file of files) {
        // 清理临时文件和日志文件
        if (file.type === 'Log' || file.name.includes('temp') || file.name.includes('cache')) {
          try {
            fileSystemManager.unlinkSync(file.fullPath)
            cleanedCount++
            cleanedSize += file.size || 0
          } catch (error) {
            console.warn('删除文件失败:', file.fullPath, error)
          }
        }
      }

      this.setData({ loading: false })

      wx.showToast({
        title: `清理完成，删除${cleanedCount}个文件`,
        icon: 'success'
      })

      this.addLog('info', `清理完成，删除${cleanedCount}个文件，释放${this.formatBytes(cleanedSize)}`)

      // 重新扫描
      this.scanFileSystem()

    } catch (error) {
      this.setData({ loading: false })
      this.addLog('error', `清理临时文件失败: ${error.message}`)
    }
  }
})
