// 日历页面逻辑 - 重构版
const { formatDate, formatDateKey, formatTime, formatDuration, getDateStart, isSameDay, calculateHourlyRate, minutesToTimeDisplay, timeStringToMinutes } = require('../../utils/helpers/time-utils.js')
const { TimeSegmentService } = require('../../core/services/time-segment-service.js')
const { WorkHistoryService } = require('../../core/services/work-history-service.js')
const { getHolidayManager } = require('../../core/managers/holiday-manager.js')
const incomeAdjustmentService = require('../../core/services/income-adjustment-service.js')

Page({
  data: {
    // 当前显示的年月
    currentYear: 2024,
    currentMonth: 1,
    
    // 日历数据
    calendarDays: [],
    weekdays: ['日', '一', '二', '三', '四', '五', '六'],
    
    // 选中的日期和数据
    selectedDate: null,
    selectedDateText: '',
    selectedDateKey: '',
    selectedDayData: null,
    
    // 工作履历相关
    currentWork: null,
    currentWorkId: null,
    currentWorkDisplayName: '',
    
    // 时间段显示数据
    displaySegments: [],

    // 摸鱼显示数据
    displayFishes: [],

    // 时间图表数据
    chartSegments: [],
    
    // 有数据的日期
    datesWithData: [],
    
    // 今天
    today: null,
    
    // 模态框状态
    showScheduleModal: false,
    showDateTypeSelector: false,
    showTimeRangePicker: false,
    showIncomeAdjustmentModal: false,

    // 模态框动画状态
    scheduleModalVisible: false,
    importModalVisible: false,
    batchModalVisible: false,
    smartIncomeModalVisible: false,
    dailyIncomeCalculatorModalVisible: false,

    // 时间段选择器相关
    editingTimeIndex: -1, // 正在编辑的时间段索引

    // 收入调整相关
    adjustmentModalMode: 'income', // 'income' 或 'deduction'
    adjustmentModalIsEdit: false,  // 是否为编辑模式
    adjustmentModalEditItem: null, // 编辑的项目数据
    adjustmentModalDateString: '', // 传递给模态框的日期字符串
    selectedDayAdjustmentSummary: null,
    
    // 工作计划表单
    dailyIncome: 0,
    timeInputs: [],
    typeOptions: [
      { value: 'work', text: '工作', icon: '💼' },
      { value: 'rest', text: '休息', icon: '☕' },
      { value: 'overtime', text: '加班', icon: '🌙' }
    ],

    // 总收入和冲突检测
    totalIncome: 0,
    totalIncomeText: '0.00',
    hasTimeConflict: false,

    // 时间统计
    workHours: 0,
    workHoursText: '0小时',
    restHours: 0,
    restHoursText: '0小时',
    overtimeHours: 0,
    overtimeHoursText: '0小时',
    
    // 日期状态相关
    dateStatus: 'work',
    statusOptions: [],
    statusIndex: 0,
    selectedStatusConfig: null,
    
    // 导入安排相关
    showImportModal: false,
    importCalendarYear: new Date().getFullYear(),
    importCalendarMonth: new Date().getMonth() + 1,
    importCalendarDays: [],
    importHasAnyData: false,
    templateDates: [],
    selectedImportDate: null,
    selectedImportDateData: null,
    selectedImportDateObject: null,
    
    // 批量操作相关
    showBatchModal: false,
    batchOperation: 'copy', // copy, import
    selectedDates: [],
    selectedSourceDate: null,
    copyStatus: true,
    copyIncome: true,
    startDate: '',
    endDate: '',
    
    // 批量复制步骤控制
    batchStep: 1, // 1: 选择源日期, 2: 选择目标日期

    // 智能填写收入相关
    showSmartIncomeModal: false,
    smartIncomeMode: 'total', // total, overtime, hourly
    smartIncomeTotalAmount: '',
    smartIncomeBaseHourly: '',
    smartIncomeOvertimeRate: '1.5',
    smartIncomeWorkHourly: '',
    smartIncomeOvertimeCalculationMethod: 'hourly', // hourly: 基础时薪方式, total: 总收入方式
    smartIncomeOvertimeHourly: '',
    smartIncomeOvertimeTotalAmount: '',

    // 日收入计算器相关
    showDailyIncomeCalculatorModal: false,
    dailyIncomeCalculatorWorkDays: '21.75', // 默认工作天数
    dailyIncomeCalculatorMonthlyIncome: '10000', // 默认月收入
    dailyIncomeCalculatorResult: '0.00', // 计算结果
    dailyIncomeCalculatorTargetMode: null, // 目标模式：'total' 或 'overtime'

    // 批量操作日历相关
    batchCalendarYear: new Date().getFullYear(),
    batchCalendarMonth: new Date().getMonth() + 1,
    batchCalendarDays: [],
    selectedTargetDates: [], // 选中的目标日期
    sourceSchedulePreview: null, // 源日期的时间安排预览
    currentMonthWorkdays: 0, // 当前月份的工作日总数
    currentMonthSelectedWorkdays: 0, // 当前月份已选择的工作日数量
    hasDataConflict: false, // 是否有数据冲突
    conflictDatesCount: 0, // 冲突日期数量
    batchCopyInProgress: false, // 批量复制是否正在进行
    batchCopyCompleted: false, // 批量复制是否已完成
    showScrollIndicator: false, // 是否显示滚动指示器
    scrollIndicatorTimer: null, // 滚动指示器定时器
    hasShownScrollIndicator: false, // 是否已经显示过滚动指示器（单次模态框内）
    
    // 统计信息
    todayIncome: '0.00',
    monthIncome: '0.00',
    
    // 时间统计
    workTime: '0小时',
    restTime: '0小时',
    overtimeTime: '0小时',

    // 摸鱼统计
    fishingStats: {
      count: 0,
      duration: '0分钟',
      income: '0.00'
    },

    // 引导界面
    hasWorkHistory: true,

    // 节假日相关
    isHolidayLoading: false,

    // 摸鱼编辑器
    showFishingEditor: false,
    fishingEditorMode: 'add', // add, edit
    editingFishing: null
  },

  /**
   * 生成动态样式配置
   */
  generateDynamicStyles() {
    try {
      const categories = this.timeSegmentService.getDateStatusCategories()
      const statusStyleMap = {}

      categories.forEach(category => {
        const colorConfig = this.timeSegmentService.getCategoryColorConfig(category.id)

        category.types.forEach(type => {
          statusStyleMap[type.value] = {
            bgColor: colorConfig.bgColor || '#F3F4F6',
            textColor: colorConfig.color || '#374151'
          }
        })
      })

      // 将样式配置存储到 data 中，供模板使用
      this.setData({
        statusStyleMap: statusStyleMap
      })

      console.log('动态样式配置生成完成:', statusStyleMap)
    } catch (error) {
      console.error('生成动态样式配置失败:', error)
    }
  },

  /**
   * 页面加载时
   */
  onLoad(options) {
    console.log('日历页面加载开始')

    // 初始化服务
    this.timeSegmentService = new TimeSegmentService()
    this.workHistoryService = new WorkHistoryService()
    this.holidayManager = getHolidayManager()

    // 获取全局数据管理器
    this.dataManager = getApp().getDataManager()

    // 生成动态样式
    this.generateDynamicStyles()

    // 设置当前日期
    const now = new Date()
    this.setData({
      currentYear: now.getFullYear(),
      currentMonth: now.getMonth() + 1,
      today: getDateStart(now),
      selectedDate: getDateStart(now),
      selectedDateText: formatDate(now),
      selectedDateKey: getDateStart(now).toISOString()
    })

    // 加载当前工作履历
    this.loadCurrentWork()

    // 加载数据
    this.loadCalendarData()
    this.loadSelectedDateData()
    this.updateStatistics()
    
    // 加载状态选项
    this.loadStatusOptions()
    
    // 初始化节假日数据
    this.initializeHolidayData()
    
    // 注册数据变化监听器
    this.registerDataChangeListener()
    
    console.log('日历页面加载完成')
  },

  /**
   * 页面显示时
   */
  onShow() {
    console.log('日历页面显示')
    
    // 刷新数据
    this.loadCurrentWork()
    this.loadCalendarData()
    this.loadSelectedDateData()
    this.updateStatistics()
  },

  /**
   * 页面卸载时
   */
  onUnload() {
    console.log('日历页面卸载')
    
    // 移除数据变化监听器
    this.unregisterDataChangeListener()
  },

  /**
   * 注册数据变化监听器
   */
  registerDataChangeListener() {
    this.dataChangeListener = (userData) => {
      console.log('收到数据变化通知，刷新日历页面数据')
      this.loadCurrentWork()
      this.loadCalendarData()
      this.loadSelectedDateData()
      this.updateStatistics()
    }
    
    this.dataManager.addChangeListener(this.dataChangeListener)
  },

  /**
   * 移除数据变化监听器
   */
  unregisterDataChangeListener() {
    if (this.dataChangeListener) {
      this.dataManager.removeChangeListener(this.dataChangeListener)
      this.dataChangeListener = null
    }
  },

  /**
   * 加载当前工作履历
   */
  loadCurrentWork() {
    try {
      // 检查是否有工作履历
      const hasWorkHistory = this.workHistoryService.hasWorkHistory()
      
      if (!hasWorkHistory) {
        // 没有工作履历，显示引导界面
        this.setData({
          hasWorkHistory: false,
          currentWork: null,
          currentWorkId: null,
          currentWorkDisplayName: '无工作履历'
        })
        return
      }
      
      // 有工作履历，加载当前工作
      const currentWork = this.workHistoryService.ensureCurrentWork()
      const displayName = this.workHistoryService.getWorkDisplayName(currentWork)
      
      this.setData({
        hasWorkHistory: true,
        currentWork,
        currentWorkId: currentWork ? currentWork.id : null,
        currentWorkDisplayName: displayName
      })
      
      console.log('当前工作履历:', displayName)
    } catch (error) {
      console.error('加载当前工作履历失败:', error)
      wx.showToast({
        title: '加载工作履历失败',
        icon: 'none'
      })
    }
  },

  /**
   * 跳转到工作履历页面
   */
  goToWorkHistory() {
    wx.switchTab({
      url: '/pages/work-history/index'
    })
  },

  /**
   * 加载日历数据
   */
  loadCalendarData() {
    this.loadDatesWithData()
    this.generateCalendar()
  },

  /**
   * 加载有数据的日期
   */
  loadDatesWithData() {
    const dates = this.timeSegmentService.getDatesWithData()
    this.setData({
      datesWithData: dates
    })
    console.log(`加载了${dates.length}个有数据的日期`)
  },

  /**
   * 加载状态选项
   */
  loadStatusOptions() {
    const statusOptions = this.timeSegmentService.getStatusOptions()
    this.setData({
      statusOptions,
      selectedStatusConfig: statusOptions[0] || null
    })
    console.log(`加载了${statusOptions.length}个状态选项`)
  },

  /**
   * 初始化节假日数据
   */
  async initializeHolidayData() {
    try {
      this.setData({ isHolidayLoading: true })
      
      // 初始化节假日管理器
      await this.holidayManager.initialize()
      
      // 重新生成日历以包含节假日信息
      this.generateCalendar()
      
      // 调试：打印节假日管理器状态
      this.holidayManager.debugStatus()
      
      console.log('节假日数据初始化完成')
    } catch (error) {
      console.error('初始化节假日数据失败:', error)
      wx.showToast({
        title: '节假日数据加载失败',
        icon: 'none',
        duration: 2000
      })
    } finally {
      this.setData({ isHolidayLoading: false })
    }
  },

  /**
   * 生成日历
   */
  generateCalendar() {
    const { currentYear, currentMonth } = this.data
    
    // 获取当月第一天和最后一天
    const firstDay = new Date(currentYear, currentMonth - 1, 1)
    const lastDay = new Date(currentYear, currentMonth, 0)
    
    // 获取当月第一天是星期几（0-6，0是星期日）
    const firstDayOfWeek = firstDay.getDay()
    
    // 获取当月天数
    const daysInMonth = lastDay.getDate()
    
    // 生成日历数据
    const calendarDays = []
    
    // 添加上个月的空白天数
    for (let i = 0; i < firstDayOfWeek; i++) {
      calendarDays.push(null)
    }
    
    // 添加当月的天数
    for (let day = 1; day <= daysInMonth; day++) {
      const date = new Date(currentYear, currentMonth - 1, day)
      const hasData = this.hasDataOnDate(date)
      const isToday = isSameDay(date, this.data.today)
      const isSelected = isSameDay(date, this.data.selectedDate)
      
      // 获取日期状态
      const status = this.timeSegmentService.getDateStatus(date, this.data.currentWorkId)
      const statusConfig = status ? this.timeSegmentService.getDateStatusConfig(status) : null
      
      // 获取节假日信息
      let holidayInfo = null
      if (this.holidayManager) {
        holidayInfo = this.holidayManager.getDateInfo(date)

        // 调试日志 - 只对特定日期打印
        const dateStr = formatDateKey(date)
        if (dateStr === '2025-01-01' || dateStr === '2025-01-28' || dateStr.includes('2025-02-0')) {
          console.log(`日历生成 ${dateStr} 节假日信息:`, holidayInfo)
        }
      }

      // 检查是否在任职日期范围内
      const isInEmploymentRange = this.isDateInEmploymentRange(date)

      // 检查是否是发薪日
      const isPayDay = this.isPayDay(date)

      calendarDays.push({
        day,
        date,
        hasData,
        isToday,
        isSelected,
        isCurrentMonth: true,
        status,
        statusConfig,
        holidayInfo,
        isInEmploymentRange,
        isPayDay
      })
    }
    
    this.setData({
      calendarDays
    })
    
    console.log(`生成${currentYear}年${currentMonth}月日历，共${calendarDays.length}天`)
  },

  /**
   * 检查指定日期是否有数据
   */
  hasDataOnDate(date) {
    return this.data.datesWithData.some(dataDate =>
      isSameDay(date, dataDate)
    )
  },

  /**
   * 检查指定日期是否是发薪日
   */
  isPayDay(date) {
    try {
      const currentWorkId = this.workHistoryService.getCurrentWorkId()
      if (!currentWorkId) {
        return false
      }

      const payDays = this.workHistoryService.getPayDays(currentWorkId)
      if (!payDays || payDays.length === 0) {
        return false
      }

      const day = date.getDate()
      return payDays.some(payDay => payDay.day === day)
    } catch (error) {
      console.error('检查发薪日失败:', error)
      return false
    }
  },

  /**
   * 检查指定日期是否在当前工作的任职日期范围内
   * @param {Date} date - 要检查的日期
   * @returns {boolean} 是否在任职范围内
   */
  isDateInEmploymentRange(date) {
    const currentWork = this.data.currentWork
    if (!currentWork) {
      return false
    }

    // 将日期标准化为只包含年月日，去除时间部分
    const checkDate = new Date(date)
    checkDate.setHours(0, 0, 0, 0)
    const startDate = new Date(currentWork.startDate)
    startDate.setHours(0, 0, 0, 0)

    // 检查是否在入职日期之后（包括入职当天）
    if (checkDate < startDate) {
      return false
    }

    // 如果有离职日期，检查是否在离职日期之前（包括离职当天）
    if (currentWork.endDate) {
      const endDate = new Date(currentWork.endDate)
      endDate.setHours(0, 0, 0, 0)
      if (checkDate > endDate) {
        return false
      }
    }

    return true
  },

  /**
   * 加载选中日期的数据
   */
  loadSelectedDateData() {
    if (!this.data.selectedDate) return
    
    const dayData = this.timeSegmentService.getDayData(this.data.selectedDate, this.data.currentWorkId)

    // 格式化时间段数据用于显示
    const displaySegments = dayData.segments.map(segment => {
      const income = segment.income || 0
      const hourlyRate = calculateHourlyRate(segment) || 0

      const startTime = minutesToTimeDisplay(segment.start)
      const endTime = minutesToTimeDisplay(segment.end)
      const duration = formatDuration(segment.end - segment.start)

      return Object.assign({}, segment, {
        startTime: startTime,
        endTime: endTime,
        duration: duration,
        typeText: this.getTypeText(segment.type),
        income: income,
        hourlyRate: hourlyRate,
        incomeText: income.toFixed(2),
        hourlyRateText: hourlyRate > 0 ? hourlyRate.toFixed(2) : ''
      })
    })

    // 格式化摸鱼数据用于显示
    const displayFishes = (dayData.fishes || []).map(fish => {
      const startTime = minutesToTimeDisplay(fish.start)
      const endTime = minutesToTimeDisplay(fish.end)
      const duration = formatDuration(fish.end - fish.start)

      // 找到摸鱼时间对应的工作时间段
      const correspondingSegment = dayData.segments.find(segment => {
        return segment.type !== 'rest' &&
               fish.start >= segment.start &&
               fish.end <= segment.end
      })

      // 计算摸鱼时薪和价值
      let hourlyRate = 0
      let fishingValue = 0

      if (correspondingSegment) {
        const segmentDuration = correspondingSegment.end - correspondingSegment.start
        const segmentHours = segmentDuration / 60
        hourlyRate = segmentHours > 0 ? (correspondingSegment.income || 0) / segmentHours : 0

        const fishingMinutes = fish.end - fish.start
        const fishingHours = fishingMinutes / 60
        fishingValue = fishingHours * hourlyRate
      }

      return Object.assign({}, fish, {
        startTime: startTime,
        endTime: endTime,
        duration: duration,
        remark: fish.remark || '',
        hourlyRate: hourlyRate,
        fishingValue: fishingValue,
        hourlyRateText: hourlyRate > 0 ? hourlyRate.toFixed(2) : '0.00',
        fishingValueText: fishingValue > 0 ? fishingValue.toFixed(2) : '0.00'
      })
    })
    
    // 为时间图表组件准备数据，转换为显示格式
    const chartSegments = dayData.segments.map(segment => {
      return Object.assign({}, segment, {
        startTime: minutesToTimeDisplay(segment.start),
        endTime: minutesToTimeDisplay(segment.end)
      })
    })
    
    // 加载选中日期的状态
    const selectedDateStatus = this.timeSegmentService.getDateStatus(this.data.selectedDate, this.data.currentWorkId) || 'work'

    // 计算状态选择器的索引
    const statusIndex = this.data.statusOptions.findIndex(item => item.value === selectedDateStatus)
    const selectedStatusConfig = this.data.statusOptions[statusIndex] || this.data.statusOptions[0]
    
    // 添加格式化的收入文本（使用动态计算）
    const dayStats = this.timeSegmentService.dataManager.timeTrackingManager.getDayDataStats(dayData)
    const selectedDayDataWithText = Object.assign({}, dayData, {
      dailyIncomeText: dayStats.dailyIncome.toFixed(2),
      netIncomeText: dayStats.netIncome.toFixed(2)
    })

    // 加载收入调整数据
    const adjustmentSummary = this.loadSelectedDateAdjustmentData()

    this.setData({
      selectedDayData: selectedDayDataWithText,
      displaySegments,
      displayFishes,
      chartSegments,  // 专门为时间图表组件准备的数据
      dateStatus: selectedDateStatus,
      statusIndex: statusIndex >= 0 ? statusIndex : 0,
      selectedStatusConfig,
      selectedDayAdjustmentSummary: adjustmentSummary
    })

    // 更新时间统计
    this.updateTimeStatistics()

    // 更新摸鱼统计
    this.updateFishingStatistics(displayFishes)

    // 获取选中日期的节假日信息
    let selectedDateInfo = { holidayInfo: null }
    if (this.holidayManager && this.data.selectedDate) {
      selectedDateInfo.holidayInfo = this.holidayManager.getDateInfo(this.data.selectedDate)
    }

    this.setData({ selectedDateInfo })

    console.log(`加载了${formatDate(this.data.selectedDate)}的数据`)
  },

  /**
   * 更新统计信息
   */
  updateStatistics() {
    try {
      // 如果没有当前工作，设置默认值
      if (!this.data.currentWorkId) {
        this.setData({
          todayIncome: '0.00',
          monthIncome: '0.00'
        })
        return
      }

      const today = new Date()
      const currentMonth = new Date(today.getFullYear(), today.getMonth(), 1)
      const nextMonth = new Date(today.getFullYear(), today.getMonth() + 1, 1)

      // 计算今日收入（使用动态计算）
      const todayData = this.timeSegmentService.getDayData(today, this.data.currentWorkId)
      const todayStats = this.timeSegmentService.dataManager.timeTrackingManager.getDayDataStats(todayData)
      const todayIncome = todayStats.netIncome.toFixed(2)

      // 计算本月收入
      const monthlyStats = this.timeSegmentService.calculateIncomeStatistics(
        [currentMonth, nextMonth], 
        this.data.currentWorkId
      )
      const monthIncome = (monthlyStats.totalIncome || 0).toFixed(2)

      this.setData({
        todayIncome,
        monthIncome
      })
      
      console.log(`日历统计更新完成 - 今日: ¥${todayIncome}, 本月: ¥${monthIncome}`)
    } catch (error) {
      console.error('更新统计信息失败:', error)
      // 设置默认值
      this.setData({
        todayIncome: '0.00',
        monthIncome: '0.00'
      })
    }
  },

  /**
   * 更新时间统计信息
   */
  updateTimeStatistics() {
    if (!this.data.selectedDayData || this.data.selectedDayData.segments.length === 0) {
      this.setData({
        workTime: '0小时',
        restTime: '0小时',
        overtimeTime: '0小时'
      })
      return
    }

    let workMinutes = 0
    let restMinutes = 0
    let overtimeMinutes = 0

    this.data.selectedDayData.segments.forEach(segment => {
      const duration = segment.end - segment.start

      switch (segment.type) {
        case 'work':
          workMinutes += duration
          break
        case 'rest':
          restMinutes += duration
          break
        case 'overtime':
          overtimeMinutes += duration
          break
      }
    })

    this.setData({
      workTime: formatDuration(workMinutes),
      restTime: formatDuration(restMinutes),
      overtimeTime: formatDuration(overtimeMinutes)
    })
  },

  /**
   * 更新摸鱼统计信息
   */
  updateFishingStatistics(displayFishes) {
    if (!displayFishes || displayFishes.length === 0) {
      this.setData({
        fishingStats: {
          count: 0,
          duration: '0分钟',
          income: '0.00'
        }
      })
      return
    }

    // 计算摸鱼次数
    const count = displayFishes.length

    // 计算摸鱼总时长
    let totalMinutes = 0
    let totalIncome = 0

    displayFishes.forEach(fish => {
      // 计算时长（从原始数据）
      const duration = fish.end - fish.start
      totalMinutes += duration

      // 计算收入
      if (fish.fishingValue) {
        totalIncome += fish.fishingValue
      }
    })

    // 格式化时长
    const duration = formatDuration(totalMinutes)

    // 格式化收入
    const income = totalIncome.toFixed(2)

    this.setData({
      fishingStats: {
        count,
        duration,
        income
      }
    })
  },

  /**
   * 获取类型文本
   */
  getTypeText(type) {
    const typeMap = {
      work: '工作',
      rest: '休息',
      overtime: '加班'
    }
    return typeMap[type] || type
  },

  /**
   * 获取类型图标
   */
  getTypeIcon(type) {
    const iconMap = {
      work: '💼',
      rest: '☕',
      overtime: '🌙'
    }
    return iconMap[type] || '🕐'
  },

  /**
   * 显示滚动提示并设置定时器
   */
  showScrollIndicatorWithTimer() {
    // 清除之前的定时器
    if (this.data.scrollIndicatorTimer) {
      clearTimeout(this.data.scrollIndicatorTimer)
    }

    // 显示提示
    this.setData({
      showScrollIndicator: true
    })

    // 3秒后隐藏
    const timer = setTimeout(() => {
      this.setData({
        showScrollIndicator: false,
        scrollIndicatorTimer: null
      })
    }, 3000)

    this.setData({
      scrollIndicatorTimer: timer
    })
  },

  /**
   * 批量模态框滚动事件
   */
  onBatchModalScroll(e) {
    if (this.data.batchStep === 1 && this.data.showScrollIndicator) {
      // 当用户开始滚动时立即隐藏指示器
      if (this.data.scrollIndicatorTimer) {
        clearTimeout(this.data.scrollIndicatorTimer)
      }
      this.setData({
        showScrollIndicator: false,
        scrollIndicatorTimer: null
      })
    }
  },

  /**
   * 上一个月
   */
  onPreviousMonth() {
    let { currentYear, currentMonth } = this.data
    
    currentMonth--
    if (currentMonth < 1) {
      currentMonth = 12
      currentYear--
    }
    
    this.setData({
      currentYear,
      currentMonth
    })
    
    this.generateCalendar()
  },

  /**
   * 下一个月
   */
  onNextMonth() {
    let { currentYear, currentMonth } = this.data
    
    currentMonth++
    if (currentMonth > 12) {
      currentMonth = 1
      currentYear++
    }
    
    this.setData({
      currentYear,
      currentMonth
    })
    
    this.generateCalendar()
  },

  /**
   * 上一年
   */
  onPreviousYear() {
    let { currentYear } = this.data
    currentYear--
    this.setData({
      currentYear
    })
    this.generateCalendar()
  },

  /**
   * 下一年
   */
  onNextYear() {
    let { currentYear } = this.data
    currentYear++
    this.setData({
      currentYear
    })
    this.generateCalendar()
  },

  /**
   * 回到今天
   */
  onToday() {
    const now = new Date()
    this.setData({
      currentYear: now.getFullYear(),
      currentMonth: now.getMonth() + 1,
      selectedDate: getDateStart(now),
      selectedDateText: formatDate(now),
      selectedDateKey: getDateStart(now).toISOString()
    })
    
    this.generateCalendar()
    this.loadSelectedDateData()
  },

  /**
   * 日期点击事件
   */
  onDateTap(e) {
    const { day } = e.currentTarget.dataset
    
    if (!day) return
    
    const { currentYear, currentMonth } = this.data
    const selectedDate = new Date(currentYear, currentMonth - 1, day)
    
    this.setData({
      selectedDate: getDateStart(selectedDate),
      selectedDateText: formatDate(selectedDate),
      selectedDateKey: getDateStart(selectedDate).toISOString()
    })
    
    this.generateCalendar()
    this.loadSelectedDateData()
    
    console.log('选中日期:', formatDate(selectedDate))
  },

  /**
   * 设置工作计划
   */
  onSetSchedule() {
    if (!this.data.selectedDate) {
      wx.showToast({
        title: '请先选择日期',
        icon: 'none'
      })
      return
    }
    
    // 初始化表单
    const dayData = this.data.selectedDayData
    const targetDailyIncome = dayData.dailyIncome || 500

    // 创建默认时间段并计算合理的收入分配
    const defaultTimeInput = this.createDefaultTimeInputsWithIncome(targetDailyIncome)
    
    // 获取当前日期状态
    const currentStatus = this.timeSegmentService.getDateStatus(this.data.selectedDate, this.data.currentWorkId)
    const statusIndex = this.data.statusOptions.findIndex(item => item.value === currentStatus)
    const selectedStatusConfig = this.data.statusOptions[statusIndex] || this.data.statusOptions[0]
    
    // 设置默认状态为工作日
    let defaultStatus = 'work'
    let defaultStatusIndex = this.data.statusOptions.findIndex(item => item.value === defaultStatus)
    if (defaultStatusIndex < 0) {
      defaultStatusIndex = 0
      defaultStatus = this.data.statusOptions[0]?.value || 'work'
    }
    
    this.setData({
      showScheduleModal: true,
      dailyIncome: dayData.dailyIncome || 500,
      dateStatus: dayData.segments.length > 0 ? currentStatus : defaultStatus,
      statusIndex: dayData.segments.length > 0 ? (statusIndex >= 0 ? statusIndex : defaultStatusIndex) : defaultStatusIndex,
      selectedStatusConfig: dayData.segments.length > 0 ? selectedStatusConfig : this.data.statusOptions[defaultStatusIndex],
      timeInputs: dayData.segments.length > 0 ?
        dayData.segments.map(segment => ({
          startTime: minutesToTimeDisplay(segment.start).replace('次日', ''),
          endTime: minutesToTimeDisplay(segment.end).replace('次日', ''),
          type: segment.type,
          typeIndex: this.data.typeOptions.findIndex(option => option.value === segment.type),
          income: segment.income || 0,
          incomeText: (segment.income || 0).toString(),
          hourlyRate: 0,
          hourlyRateText: '',
          isStartNextDay: segment.start >= 24 * 60,
          isEndNextDay: segment.end >= 24 * 60,
          _isEditingHourlyRate: false
        })) : defaultTimeInput
    }, () => {
      // 初始化显示数据
      this.updateAllCalculations()

      // 延迟显示动画
      setTimeout(() => {
        this.setData({ scheduleModalVisible: true })
      }, 50)
    })
  },

  /**
   * 关闭设置模态框
   */
  onCloseScheduleModal() {
    // 开始出场动画
    this.setData({ scheduleModalVisible: false })

    // 等待动画完成后关闭模态框
    setTimeout(() => {
      this.setData({
        showScheduleModal: false,
        timeInputs: []
      })
    }, 300) // 与CSS动画时长一致
  },

  /**
   * 阻止事件冒泡
   */
  onStopPropagation() {
    // 阻止点击模态框内容时关闭模态框
  },

  /**
   * 日收入改变
   */
  onDailyIncomeChange(e) {
    const value = parseFloat(e.detail.value) || 0
    this.setData({
      dailyIncome: value
    })
  },

  /**
   * 开始时间改变
   */
  onStartTimeChange(e) {
    const { index } = e.currentTarget.dataset
    const value = e.detail.value

    const timeInputs = this.data.timeInputs.slice()
    timeInputs[index].startTime = value

    this.setData({
      timeInputs
    }, () => {
      this.sortTimeInputsByStartTime()
      this.updateAllCalculations()
    })
  },

  /**
   * 结束时间改变
   */
  onEndTimeChange(e) {
    const { index } = e.currentTarget.dataset
    const value = e.detail.value

    const timeInputs = this.data.timeInputs.slice()
    timeInputs[index].endTime = value

    this.setData({
      timeInputs
    }, () => {
      this.sortTimeInputsByStartTime()
      this.updateAllCalculations()
    })
  },

  /**
   * 类型改变
   */
  onTypeChange(e) {
    const { index } = e.currentTarget.dataset
    const typeIndex = parseInt(e.detail.value)

    const timeInputs = this.data.timeInputs.slice()
    timeInputs[index].type = this.data.typeOptions[typeIndex].value
    timeInputs[index].typeIndex = typeIndex

    // 如果改为休息，清空收入
    if (timeInputs[index].type === 'rest') {
      timeInputs[index].income = 0
    }

    this.setData({
      timeInputs
    }, () => {
      this.updateAllCalculations()
    })
  },



  /**
   * 打开日期类型选择器
   */
  onOpenDateTypeSelector() {
    console.log('日历页面 - 打开日期类型选择器')
    this.setData({
      showDateTypeSelector: true
    })
  },

  /**
   * 日期类型选择器 - 选择类型
   */
  onDateTypeSelectorSelect(e) {
    const { value } = e.detail
    console.log('日历页面 - 日期类型选择器选择:', value)

    // 更新当前选中的状态
    const statusConfig = this.timeSegmentService.getDateStatusConfig(value)

    this.setData({
      dateStatus: value,
      selectedStatusConfig: statusConfig
    })
  },

  /**
   * 日期类型选择器 - 确认选择
   */
  onDateTypeSelectorConfirm(e) {
    const { value } = e.detail
    console.log('日历页面 - 日期类型选择器确认:', value)

    // 更新状态
    const statusConfig = this.timeSegmentService.getDateStatusConfig(value)

    this.setData({
      dateStatus: value,
      selectedStatusConfig: statusConfig,
      showDateTypeSelector: false
    })
  },

  /**
   * 日期类型选择器 - 取消选择
   */
  onDateTypeSelectorCancel() {
    console.log('日历页面 - 日期类型选择器取消')
    this.setData({
      showDateTypeSelector: false
    })
  },

  /**
   * 日期类型选择器 - 关闭
   */
  onDateTypeSelectorClose() {
    console.log('日历页面 - 日期类型选择器关闭')
    this.setData({
      showDateTypeSelector: false
    })
  },

  /**
   * 打开时间段选择器
   */
  onOpenTimeRangePicker(e) {
    const { index } = e.currentTarget.dataset
    const timeInput = this.data.timeInputs[index]

    console.log('日历页面 - 打开时间段选择器:', { index, timeInput })

    this.setData({
      showTimeRangePicker: true,
      editingTimeIndex: index
    })
  },

  /**
   * 时间段选择器 - 确认选择
   */
  onTimeRangePickerConfirm(e) {
    const { startTime, endTime, isStartNextDay, isEndNextDay, duration } = e.detail
    const { editingTimeIndex } = this.data

    console.log('日历页面 - 时间段选择器确认:', { startTime, endTime, isStartNextDay, isEndNextDay, duration, editingTimeIndex })

    if (editingTimeIndex >= 0) {
      const timeInputs = [...this.data.timeInputs]
      timeInputs[editingTimeIndex] = {
        ...timeInputs[editingTimeIndex],
        startTime,
        endTime,
        isStartNextDay,
        isEndNextDay,
        duration // 保存时长显示文本
      }

      this.setData({
        timeInputs,
        showTimeRangePicker: false,
        editingTimeIndex: -1
      })

      // 重新计算所有相关数据
      this.updateAllCalculations()
    }
  },

  /**
   * 时间段选择器 - 取消选择
   */
  onTimeRangePickerCancel() {
    console.log('日历页面 - 时间段选择器取消')
    this.setData({
      showTimeRangePicker: false,
      editingTimeIndex: -1
    })
  },

  /**
   * 时间段选择器 - 关闭
   */
  onTimeRangePickerClose() {
    console.log('日历页面 - 时间段选择器关闭')
    this.setData({
      showTimeRangePicker: false,
      editingTimeIndex: -1
    })
  },

  /**
   * 添加时间段
   */
  onAddTimeInput() {
    const timeInputs = this.data.timeInputs.slice()
    const lastInput = timeInputs[timeInputs.length - 1]

    let newInput = {
      startTime: lastInput ? lastInput.endTime : '09:00',
      endTime: this.addHours(lastInput ? lastInput.endTime : '09:00', 1),
      type: 'work',
      typeIndex: 0,
      income: 0,
      incomeText: '',
      hourlyRate: 0,
      hourlyRateText: '',
      isStartNextDay: false,
      isEndNextDay: false,
      _isEditingHourlyRate: false
    }

    if (lastInput) {
      // 如果最后一个时间段是工作或加班，则添加一个休息时间段
      if (lastInput.type === 'work' || lastInput.type === 'overtime') {
        newInput.type = 'rest';
        newInput.typeIndex = 1;
      } else if (lastInput.type === 'rest') {
        const endHour = parseInt(lastInput.endTime.split(':')[0]);
        if (endHour >= 18) {
          newInput.type = 'overtime';
          newInput.typeIndex = 2;
        }
      }

      // 继承上一个时间段的跨日状态
      newInput.isStartNextDay = lastInput.isEndNextDay;
    }

    // 为工作和加班时间段设置合理的默认时薪
    if (newInput.type === 'work' || newInput.type === 'overtime') {
      const defaultHourlyRate = this.calculateReasonableHourlyRate(timeInputs)
      newInput.hourlyRate = defaultHourlyRate
      newInput.hourlyRateText = defaultHourlyRate.toFixed(2)
      newInput._lastUpdatedBy = 'hourlyRate' // 标记为基于时薪设置
    }

    timeInputs.push(newInput)

    this.setData({
      timeInputs
    }, () => {
      this.sortTimeInputsByStartTime()
      this.updateAllCalculations()
    })
  },

  /**
   * 移除时间段
   */
  onRemoveTimeInput(e) {
    const { index } = e.currentTarget.dataset
    const timeInputs = this.data.timeInputs.slice()
    timeInputs.splice(index, 1)
    this.setData({
      timeInputs
    }, () => {
      this.updateAllCalculations()
    })
  },

  /**
   * 确认设置工作计划
   */
  onConfirmSchedule() {
    if (!this.data.selectedDate) {
      wx.showToast({
        title: '请先选择日期',
        icon: 'none'
      })
      return
    }

    // 检查时间冲突
    if (this.data.hasTimeConflict) {
      wx.showToast({
        title: '存在时间段重叠，请调整后再保存',
        icon: 'none'
      })
      return
    }

    // 验证时间输入
    const validationResult = this.validateTimeInputs(this.data.timeInputs)
    if (!validationResult.isValid) {
      wx.showToast({
        title: validationResult.message,
        icon: 'none'
      })
      return
    }

    // 处理收入数据，确保小数位数不超过2位
    const processedTimeInputs = this.processIncomeData(this.data.timeInputs)
    
    try {
      // 先保存日期状态，避免被工作计划保存覆盖
      console.log('保存前的日期状态:', this.data.dateStatus)
      this.timeSegmentService.setDateStatus(this.data.selectedDate, this.data.dateStatus, this.data.currentWorkId)
      console.log('状态保存后立即读取:', this.timeSegmentService.getDateStatus(this.data.selectedDate, this.data.currentWorkId))

      // 保存工作计划
      const result = this.timeSegmentService.setDaySchedule(
        this.data.selectedDate,
        processedTimeInputs,
        this.data.dailyIncome,
        this.data.currentWorkId
      )

      console.log('工作计划保存后读取状态:', this.timeSegmentService.getDateStatus(this.data.selectedDate, this.data.currentWorkId))

      // 检查是否有摸鱼数据冲突
      if (result && result.requiresFishingUpdate) {
        this.handleFishingConflicts(result)
        return
      }

      // 刷新数据
      this.loadCalendarData()
      this.loadSelectedDateData()
      this.updateStatistics()
      this.updateTimeStatistics()

      // 触发全局数据更新事件，通知主页刷新
      this.triggerDataUpdateEvent()

      // 关闭模态框
      this.onCloseScheduleModal()

      wx.showToast({
        title: '设置成功',
        icon: 'success'
      })
    } catch (error) {
      console.error('设置工作计划失败:', error)
      wx.showToast({
        title: '设置失败',
        icon: 'none'
      })
    }
  },

  /**
   * 触发全局数据更新事件
   */
  triggerDataUpdateEvent() {
    try {
      // 通过全局事件总线通知其他页面数据已更新
      const app = getApp()
      if (app.globalData.eventBus && app.globalData.eventBus.onDataUpdate) {
        app.globalData.eventBus.onDataUpdate()
      }
    } catch (error) {
      console.error('触发数据更新事件失败:', error)
    }
  },

  /**
   * 清除选中日期的数据
   */
  onClearDay() {
    if (!this.data.selectedDate) {
      wx.showToast({
        title: '请先选择日期',
        icon: 'none'
      })
      return
    }
    
    wx.showModal({
      title: '确认清除',
      content: `确定要清除 ${this.data.selectedDateText} 的所有数据吗？`,
      confirmText: '清除',
      confirmColor: '#EF4444',
      success: (res) => {
        if (res.confirm) {
          try {
            this.timeSegmentService.clearDayData(this.data.selectedDate)
            
            // 刷新数据
            this.loadCalendarData()
            this.loadSelectedDateData()
            this.updateStatistics()
            
            // 触发全局数据更新事件
            this.triggerDataUpdateEvent()
            
            wx.showToast({
              title: '清除成功',
              icon: 'success'
            })
          } catch (error) {
            console.error('清除数据失败:', error)
            wx.showToast({
              title: '清除失败',
              icon: 'none'
            })
          }
        }
      }
    })
  },



  /**
   * 时间加法辅助函数
   */
  addHours(timeStr, hours) {
    const [h, m] = timeStr.split(':').map(Number)
    const newHours = (h + hours) % 24
    return `${newHours.toString().padStart(2, '0')}:${m.toString().padStart(2, '0')}`
  },

  /**
   * 分享功能
   */
  onShareAppMessage() {
    return {
      title: '时间跟踪器 - 工作计划',
      path: '/pages/calendar/index'
    }
  },

  /**
   * 调试方法：强制更新节假日数据
   */
  async debugForceUpdateHolidays() {
    try {
      console.log('开始强制更新节假日数据...')
      await this.holidayManager.forceUpdate()
      this.generateCalendar()
      this.loadSelectedDateData()
      wx.showToast({
        title: '节假日数据更新完成',
        icon: 'success'
      })
    } catch (error) {
      console.error('强制更新失败:', error)
      wx.showToast({
        title: '更新失败: ' + error.message,
        icon: 'none'
      })
    }
  },

  // ========== 批量操作相关方法 ==========

  /**
   * 导入工作安排（在设置工作计划模态框中）
   */
  onImportFromDateInModal() {
    if (!this.data.selectedDate) {
      wx.showToast({
        title: '请先选择日期',
        icon: 'none'
      })
      return
    }

    // 获取所有有数据的日期作为模板
    const templateDates = this.timeSegmentService.getDatesWithData()
      .map(date => {
        const dayData = this.timeSegmentService.getDayData(date)
        const status = this.timeSegmentService.getDateStatus(date, this.data.currentWorkId)
        const statusConfig = this.timeSegmentService.getDateStatusConfig(status)
        
        return {
          date,
          dateKey: date.toISOString(),
          dateText: formatDate(date),
          dailyIncome: dayData.dailyIncome || 0,
          segmentCount: dayData.segments.length,
          statusConfig: {
            ...statusConfig,
            name: statusConfig.name || '无状态'
          }
        }
      })
      .filter(item => item.segmentCount > 0)
    
    const now = new Date()
    this.setData({
      showImportModal: true,
      templateDates,
      importCalendarYear: now.getFullYear(),
      importCalendarMonth: now.getMonth() + 1
    })
    
    // 生成导入日历
    this.generateImportCalendar()
  },

  /**
   * 生成导入日历
   */
  generateImportCalendar() {
    const { importCalendarYear, importCalendarMonth } = this.data
    
    // 获取当月第一天和最后一天
    const firstDay = new Date(importCalendarYear, importCalendarMonth - 1, 1)
    const lastDay = new Date(importCalendarYear, importCalendarMonth, 0)
    
    // 获取当月第一天是星期几（0-6，0是星期日）
    const firstDayOfWeek = firstDay.getDay()
    
    // 获取当月天数
    const daysInMonth = lastDay.getDate()
    
    // 生成日历数据
    const calendarDays = []
    let hasAnyData = false
    
    // 添加上个月的空白天数
    for (let i = 0; i < firstDayOfWeek; i++) {
      calendarDays.push(null)
    }
    
    // 添加当月的天数
    for (let day = 1; day <= daysInMonth; day++) {
      const date = new Date(importCalendarYear, importCalendarMonth - 1, day)
      const dayData = this.timeSegmentService.getDayData(date)
      const hasData = dayData.segments.length > 0
      const status = this.timeSegmentService.getDateStatus(date, this.data.currentWorkId)
      const statusConfig = status ? this.timeSegmentService.getDateStatusConfig(status) : null

      // 检查是否在任职日期范围内
      const isInEmploymentRange = this.isDateInEmploymentRange(date)

      if (hasData) {
        hasAnyData = true
      }
      
      calendarDays.push({
        day,
        date,
        hasData,
        isCurrentMonth: true,
        status,
        statusConfig,
        dailyIncome: dayData.dailyIncome || 0,
        segmentCount: dayData.segments.length,
        isInEmploymentRange
      })
    }
    
    this.setData({
      importCalendarDays: calendarDays,
      importHasAnyData: hasAnyData
    })
  },

  /**
   * 导入日历上一个月
   */
  onImportPreviousMonth() {
    let { importCalendarYear, importCalendarMonth } = this.data
    
    importCalendarMonth--
    if (importCalendarMonth < 1) {
      importCalendarMonth = 12
      importCalendarYear--
    }
    
    this.setData({
      importCalendarYear,
      importCalendarMonth
    })
    
    this.generateImportCalendar()
  },

  /**
   * 导入日历下一个月
   */
  onImportNextMonth() {
    let { importCalendarYear, importCalendarMonth } = this.data
    
    importCalendarMonth++
    if (importCalendarMonth > 12) {
      importCalendarMonth = 1
      importCalendarYear++
    }
    
    this.setData({
      importCalendarYear,
      importCalendarMonth
    })
    
    this.generateImportCalendar()
  },

  /**
   * 导入日历上一年
   */
  onImportPreviousYear() {
    let { importCalendarYear } = this.data
    importCalendarYear--
    this.setData({
      importCalendarYear
    })
    this.generateImportCalendar()
  },

  /**
   * 导入日历下一年
   */
  onImportNextYear() {
    let { importCalendarYear } = this.data
    importCalendarYear++
    this.setData({
      importCalendarYear
    })
    this.generateImportCalendar()
  },

  /**
   * 导入日历日期点击
   */
  onImportDateTap(e) {
    const { index } = e.currentTarget.dataset
    const dayItem = this.data.importCalendarDays[index]
    
    if (!dayItem || !dayItem.isCurrentMonth || !dayItem.hasData) {
      if (dayItem && dayItem.isCurrentMonth && !dayItem.hasData) {
        wx.showToast({
          title: '该日期没有工作安排',
          icon: 'none'
        })
      }
      return
    }
    
    const sourceDate = dayItem.date
    
    // 获取日期详情数据
    const sourceDayData = this.timeSegmentService.getDayData(sourceDate)
    const sourceStatus = this.timeSegmentService.getDateStatus(sourceDate, this.data.currentWorkId)
    const sourceStatusConfig = this.timeSegmentService.getDateStatusConfig(sourceStatus)
    
    // 格式化时间段信息
    const formattedSegments = sourceDayData.segments.map(segment => ({
      ...segment,
      startTime: formatTime(segment.start),
      endTime: formatTime(segment.end)
    }))
    
    // 更新选中的日期信息
    this.setData({
      selectedImportDate: formatDate(sourceDate),
      selectedImportDateObject: sourceDate,
      selectedImportDateData: {
        ...sourceDayData,
        segments: formattedSegments,
        status: sourceStatus,
        statusConfig: sourceStatusConfig
      }
    })
  },

  /**
   * 从指定日期导入工作安排
   */
  importScheduleFromDate(sourceDate) {
    try {
      const sourceDayData = this.timeSegmentService.getDayData(sourceDate)
      const sourceStatus = this.timeSegmentService.getDateStatus(sourceDate, this.data.currentWorkId)
      const sourceStatusConfig = this.timeSegmentService.getDateStatusConfig(sourceStatus)
      
      if (sourceDayData.segments.length === 0) {
        wx.showToast({
          title: '该日期没有工作安排',
          icon: 'none'
        })
        return
      }
      
      // 转换时间段数据为表单格式
      const timeInputs = sourceDayData.segments.map(segment => ({
        startTime: minutesToTimeDisplay(segment.start).replace('次日', ''),
        endTime: minutesToTimeDisplay(segment.end).replace('次日', ''),
        type: segment.type,
        typeIndex: this.data.typeOptions.findIndex(option => option.value === segment.type),
        income: segment.income || 0,
        hourlyRate: 0,
        hourlyRateText: '',
        isStartNextDay: segment.start >= 24 * 60,
        isEndNextDay: segment.end >= 24 * 60
      }))
      
      // 获取状态索引
      const statusIndex = this.data.statusOptions.findIndex(item => item.value === sourceStatus)
      const selectedStatusConfig = this.data.statusOptions[statusIndex] || this.data.statusOptions[0]
      
      // 更新表单数据
      this.setData({
        timeInputs,
        dailyIncome: sourceDayData.dailyIncome || 0,
        dateStatus: sourceStatus,
        statusIndex: statusIndex >= 0 ? statusIndex : 0,
        selectedStatusConfig
      }, () => {
        // 更新显示信息
        this.updateAllCalculations()
      })
      
      wx.showToast({
        title: '导入成功',
        icon: 'success'
      })
      
    } catch (error) {
      console.error('导入失败:', error)
      wx.showToast({
        title: '导入失败',
        icon: 'none'
      })
    }
  },

  /**
   * 确定导入选中的日期
   */
  onConfirmImport() {
    if (!this.data.selectedImportDate || !this.data.selectedImportDateObject) {
      wx.showToast({
        title: '请先选择要导入的日期',
        icon: 'none'
      })
      return
    }
    
    // 导入选中的日期（使用原始Date对象）
    this.importScheduleFromDate(this.data.selectedImportDateObject)
    
    // 关闭导入模态框
    this.onCloseImportModal()
  },

  /**
   * 关闭导入模态框
   */
  onCloseImportModal() {
    this.setData({
      showImportModal: false,
      importCalendarDays: [],
      templateDates: [],
      selectedImportDate: null,
      selectedImportDateData: null,
      selectedImportDateObject: null
    })
  },

  /**
   * 导入工作安排（保留原有功能）
   */
  onImportFromDate() {
    if (!this.data.selectedDate) {
      wx.showToast({
        title: '请先选择日期',
        icon: 'none'
      })
      return
    }

    // 获取所有有数据的日期作为模板
    const templateDates = this.timeSegmentService.getDatesWithData()
      .map(date => {
        const dayData = this.timeSegmentService.getDayData(date)
        const status = this.timeSegmentService.getDateStatus(date, this.data.currentWorkId)
        const statusConfig = this.timeSegmentService.getDateStatusConfig(status)
        
        return {
          date,
          dateKey: date.toISOString(),
          dateText: formatDate(date),
          dailyIncome: dayData.dailyIncome || 0,
          segmentCount: dayData.segments.length,
          statusConfig: {
            ...statusConfig,
            name: statusConfig.name || '无状态'
          }
        }
      })
      .filter(item => item.segmentCount > 0)
    
    this.setData({
      showBatchModal: true,
      batchOperation: 'import',
      templateDates
    })

    // 延迟显示动画
    setTimeout(() => {
      this.setData({ batchModalVisible: true })
    }, 50)
  },

  /**
   * 批量复制
   */
  onBatchCopy() {
    console.log('批量复制')
    
    // 重置批量复制状态
    const now = new Date()
    this.setData({
      showBatchModal: true,
      batchOperation: 'copy',
      batchStep: 1,
      batchCalendarYear: now.getFullYear(),
      batchCalendarMonth: now.getMonth() + 1,
      selectedSourceDate: null,
      selectedTargetDates: [],
      sourceSchedulePreview: null,
      copyStatus: true,
      copyIncome: true,
      showScrollIndicator: false,
      hasShownScrollIndicator: false
    })

    // 生成批量复制日历
    this.generateBatchCalendar()

    // 延迟显示动画
    setTimeout(() => {
      this.setData({ batchModalVisible: true })
    }, 50)
  },

  /**
   * 生成批量复制日历
   */
  generateBatchCalendar() {
    const { batchCalendarYear, batchCalendarMonth } = this.data
    
    // 获取当月第一天和最后一天
    const firstDay = new Date(batchCalendarYear, batchCalendarMonth - 1, 1)
    const lastDay = new Date(batchCalendarYear, batchCalendarMonth, 0)
    
    // 获取当月第一天是星期几（0-6，0是星期日）
    const firstDayOfWeek = firstDay.getDay()
    
    // 获取当月天数
    const daysInMonth = lastDay.getDate()
    
    // 计算当月工作日总数（用于按钮显示）
    let currentMonthWorkdays = 0
    // 计算当月已选择的工作日数量
    let currentMonthSelectedWorkdays = 0
    
    // 生成日历数据
    const calendarDays = []
    
    // 添加上个月的空白天数
    for (let i = 0; i < firstDayOfWeek; i++) {
      calendarDays.push(null)
    }
    
    // 添加当月的天数
    for (let day = 1; day <= daysInMonth; day++) {
      const date = new Date(batchCalendarYear, batchCalendarMonth - 1, day)
      const dayData = this.timeSegmentService.getDayData(date)
      const hasData = dayData.segments.length > 0
      const status = this.timeSegmentService.getDateStatus(date, this.data.currentWorkId)
      const statusConfig = status ? this.timeSegmentService.getDateStatusConfig(status) : null
      
      // 检查是否被选中为目标日期
      const isSelectedTarget = this.data.selectedTargetDates.some(targetDate => 
        targetDate.toDateString() === date.toDateString()
      )
      
      // 检查是否是选中的源日期
      const isSelectedSource = this.data.selectedSourceDate && 
        this.data.selectedSourceDate.toDateString() === date.toDateString()
      
      // 判断是否为工作日（用于统计）
      let isWorkday = false
      if (this.holidayManager) {
        const holidayInfo = this.holidayManager.getDateInfo(date)
        if (holidayInfo && holidayInfo.type !== 'normal') {
          isWorkday = holidayInfo.isWork
        } else if (holidayInfo && holidayInfo.type === 'normal') {
          isWorkday = true
        } else {
          const dayOfWeek = date.getDay()
          isWorkday = dayOfWeek >= 1 && dayOfWeek <= 5
        }
      } else {
        const dayOfWeek = date.getDay()
        isWorkday = dayOfWeek >= 1 && dayOfWeek <= 5
      }
      
      if (isWorkday) {
        currentMonthWorkdays++
        // 如果这个工作日被选中，增加已选择的工作日计数
        if (isSelectedTarget) {
          currentMonthSelectedWorkdays++
        }
      }

      // 检查是否在任职日期范围内
      const isInEmploymentRange = this.isDateInEmploymentRange(date)

      // 检查是否是今天
      const today = new Date()
      const isToday = date.toDateString() === today.toDateString()

      calendarDays.push({
        day,
        date,
        hasData,
        isCurrentMonth: true,
        status,
        statusConfig,
        isSelectedTarget,
        isSelectedSource,
        dailyIncome: dayData.dailyIncome || 0,
        segmentCount: dayData.segments.length,
        isWorkday,
        isInEmploymentRange,
        isToday
      })
    }
    
    // 检测数据冲突（仅在步骤2时检测）
    let hasDataConflict = false
    let conflictDatesCount = 0

    if (this.data.batchStep === 2 && this.data.selectedTargetDates && this.data.selectedTargetDates.length > 0) {
      conflictDatesCount = this.data.selectedTargetDates.filter(targetDate => {
        const dayData = this.timeSegmentService.getDayData(targetDate)
        return (dayData.segments && dayData.segments.length > 0) ||
               (dayData.fishingRecords && dayData.fishingRecords.length > 0) ||
               (dayData.extraIncomeItems && dayData.extraIncomeItems.length > 0) ||
               (dayData.deductionItems && dayData.deductionItems.length > 0)
      }).length

      hasDataConflict = conflictDatesCount > 0
    }

    this.setData({
      batchCalendarDays: calendarDays,
      currentMonthWorkdays: currentMonthWorkdays,
      currentMonthSelectedWorkdays: currentMonthSelectedWorkdays,
      hasDataConflict: hasDataConflict,
      conflictDatesCount: conflictDatesCount
    })
  },

  /**
   * 批量复制日历上月
   */
  onBatchPreviousMonth() {
    let { batchCalendarYear, batchCalendarMonth } = this.data
    
    batchCalendarMonth--
    if (batchCalendarMonth < 1) {
      batchCalendarMonth = 12
      batchCalendarYear--
    }
    
    this.setData({
      batchCalendarYear,
      batchCalendarMonth
    })
    
    this.generateBatchCalendar()
  },

  /**
   * 批量复制日历下月
   */
  onBatchNextMonth() {
    let { batchCalendarYear, batchCalendarMonth } = this.data
    
    batchCalendarMonth++
    if (batchCalendarMonth > 12) {
      batchCalendarMonth = 1
      batchCalendarYear++
    }
    
    this.setData({
      batchCalendarYear,
      batchCalendarMonth
    })
    
    this.generateBatchCalendar()
  },

  /**
   * 批量复制日历上一年
   */
  onBatchPreviousYear() {
    let { batchCalendarYear } = this.data
    
    batchCalendarYear--
    
    this.setData({
      batchCalendarYear
    })
    
    this.generateBatchCalendar()
  },

  /**
   * 批量复制日历下一年
   */
  onBatchNextYear() {
    let { batchCalendarYear } = this.data
    
    batchCalendarYear++
    
    this.setData({
      batchCalendarYear
    })
    
    this.generateBatchCalendar()
  },

  /**
   * 添加当前月份的工作日到现有选择中
   */
  onAddCurrentMonthWorkdays() {
    const { batchCalendarYear, batchCalendarMonth, selectedTargetDates } = this.data
    const currentWorkdayDates = this.getCurrentMonthWorkdays()

    // 创建现有选择日期的字符串集合，用于快速查找
    const existingDatesSet = new Set(selectedTargetDates.map(date => date.toDateString()))

    // 过滤出尚未选择的工作日，并排除今天和有数据的日期
    const today = new Date()
    const todayString = today.toDateString()
    const newWorkdayDates = currentWorkdayDates.filter(date => {
      // 排除已选择的日期
      if (existingDatesSet.has(date.toDateString())) {
        return false
      }

      // 排除今天
      if (date.toDateString() === todayString) {
        return false
      }

      // 排除有数据的日期
      const dayData = this.timeSegmentService.getDayData(date)
      const hasData = (dayData.segments && dayData.segments.length > 0) ||
                     (dayData.fishingRecords && dayData.fishingRecords.length > 0) ||
                     (dayData.extraIncomeItems && dayData.extraIncomeItems.length > 0) ||
                     (dayData.deductionItems && dayData.deductionItems.length > 0)

      return !hasData
    })

    // 合并现有选择和新的工作日
    const updatedSelectedDates = [...selectedTargetDates, ...newWorkdayDates]

    this.setData({
      selectedTargetDates: updatedSelectedDates
    })

    this.generateBatchCalendar()

    // 显示操作结果
    const addedCount = newWorkdayDates.length
    const totalSelected = updatedSelectedDates.length

    // 计算排除的日期数量
    const totalWorkdays = currentWorkdayDates.length
    const excludedCount = totalWorkdays - addedCount - selectedTargetDates.filter(date => {
      const dateString = date.toDateString()
      return currentWorkdayDates.some(workday => workday.toDateString() === dateString)
    }).length

    if (addedCount > 0) {
      let message = `已添加本月${addedCount}个工作日，总计选择${totalSelected}个日期`
      if (excludedCount > 0) {
        message += `（已排除${excludedCount}个有数据的日期）`
      }
      wx.showToast({
        title: message,
        icon: 'success',
        duration: 3000
      })
    } else {
      wx.showToast({
        title: '本月可选择的工作日已全部选择（已排除今天和有数据的日期）',
        icon: 'none',
        duration: 2500
      })
    }

    console.log(`添加了${batchCalendarYear}年${batchCalendarMonth}月的${addedCount}个工作日，总计选择${totalSelected}个日期`)
  },

  /**
   * 仅选择当前月份的工作日（清空其他选择）
   */
  onSelectOnlyCurrentMonthWorkdays() {
    const { batchCalendarYear, batchCalendarMonth } = this.data
    const workdayDates = this.getCurrentMonthWorkdays()

    this.setData({
      selectedTargetDates: workdayDates
    })

    this.generateBatchCalendar()

    // 显示操作结果
    wx.showToast({
      title: `已清空其他选择，仅选择本月${workdayDates.length}个工作日`,
      icon: 'success',
      duration: 2500
    })

    console.log(`仅选择了${batchCalendarYear}年${batchCalendarMonth}月的${workdayDates.length}个工作日`)
  },

  /**
   * 获取当前月份的所有工作日
   */
  getCurrentMonthWorkdays() {
    const { batchCalendarYear, batchCalendarMonth } = this.data
    const workdayDates = []
    let holidayDataAvailable = false

    // 获取当月天数
    const daysInMonth = new Date(batchCalendarYear, batchCalendarMonth, 0).getDate()

    for (let day = 1; day <= daysInMonth; day++) {
      const date = new Date(batchCalendarYear, batchCalendarMonth - 1, day)

      // 优先使用节假日数据判断工作日
      let isWorkday = false

      if (this.holidayManager) {
        const holidayInfo = this.holidayManager.getDateInfo(date)

        if (holidayInfo && holidayInfo.type !== 'normal') {
          // 如果有具体的节假日信息（非普通日期）
          holidayDataAvailable = true
          isWorkday = holidayInfo.isWork
        } else if (holidayInfo && holidayInfo.type === 'normal') {
          // 普通日期，不是节假日也不是周末
          isWorkday = true
        } else {
          // 如果没有节假日信息，按照周一到周五判断
          const dayOfWeek = date.getDay()
          isWorkday = dayOfWeek >= 1 && dayOfWeek <= 5 // 周一到周五
        }
      } else {
        // 如果节假日管理器不可用，按照周一到周五判断
        const dayOfWeek = date.getDay()
        isWorkday = dayOfWeek >= 1 && dayOfWeek <= 5 // 周一到周五
      }

      if (isWorkday) {
        workdayDates.push(date)
      }
    }

    return workdayDates
  },

  /**
   * 清空选择的目标日期
   */
  onClearSelection() {
    const clearedCount = this.data.selectedTargetDates.length

    this.setData({
      selectedTargetDates: []
    })

    this.generateBatchCalendar()

    wx.showToast({
      title: `已清空${clearedCount}个选择的日期`,
      icon: 'success',
      duration: 2000
    })

    console.log(`清空了${clearedCount}个选择的日期`)
  },

  /**
   * 批量复制日历点击日期
   */
  onBatchDateTap(e) {
    const { index } = e.currentTarget.dataset
    const dayItem = this.data.batchCalendarDays[index]
    
    if (!dayItem || !dayItem.isCurrentMonth) return
    
    const date = dayItem.date
    
    if (this.data.batchStep === 1) {
      // 第一步：选择源日期
      if (!dayItem.hasData) {
        wx.showToast({
          title: '该日期没有工作安排',
          icon: 'none'
        })
        return
      }
      
      // 获取该日期的工作安排预览
      const dayData = this.timeSegmentService.getDayData(date)
      const status = this.timeSegmentService.getDateStatus(date, this.data.currentWorkId)
      const statusConfig = status ? this.timeSegmentService.getDateStatusConfig(status) : null

      const displaySegments = dayData.segments.map(segment => {
        const startTime = minutesToTimeDisplay(segment.start)
        const endTime = minutesToTimeDisplay(segment.end)
        const duration = formatDuration(segment.end - segment.start)
        const typeIcon = this.getTypeIcon(segment.type)
        const incomeText = segment.income ? segment.income.toFixed(2) : '0.00'
        const hourlyRateText = segment.hourlyRate ? segment.hourlyRate.toFixed(2) : null

        return {
          type: segment.type,
          startTime: startTime,
          endTime: endTime,
          typeText: this.getTypeText(segment.type),
          typeIcon: typeIcon,
          duration: duration,
          income: segment.income || 0,
          incomeText: incomeText,
          hourlyRate: segment.hourlyRate || 0,
          hourlyRateText: hourlyRateText
        }
      })

      this.setData({
        selectedSourceDate: date,
        sourceSchedulePreview: {
          date: formatDate(date),
          dailyIncome: dayData.dailyIncome || 0,
          statusConfig: statusConfig,
          segments: displaySegments
        }
      })

      // 只在第一次选择日期时显示滚动提示
      if (!this.data.hasShownScrollIndicator) {
        this.showScrollIndicatorWithTimer()
        this.setData({
          hasShownScrollIndicator: true
        })
      }
      
      this.generateBatchCalendar()
    } else if (this.data.batchStep === 2) {
      // 第二步：选择目标日期

      // 禁止选择今天
      if (dayItem.isToday) {
        wx.showToast({
          title: '不能选择今天',
          icon: 'none'
        })
        return
      }

      const selectedTargetDates = this.data.selectedTargetDates.slice()
      const existingIndex = selectedTargetDates.findIndex(targetDate => 
        targetDate.toDateString() === date.toDateString()
      )
      
      if (existingIndex >= 0) {
        // 已选中，取消选中
        selectedTargetDates.splice(existingIndex, 1)
      } else {
        // 未选中，添加选中
        selectedTargetDates.push(date)
      }
      
      this.setData({
        selectedTargetDates
      })
      
      this.generateBatchCalendar()
    }
  },

  /**
   * 确认选择源日期
   */
  onConfirmSourceDate() {
    if (!this.data.selectedSourceDate) {
      wx.showToast({
        title: '请选择源日期',
        icon: 'none'
      })
      return
    }
    
    this.setData({
      batchStep: 2
    })
    
    this.generateBatchCalendar()
  },

  /**
   * 返回选择源日期
   */
  onBackToSourceSelection() {
    this.setData({
      batchStep: 1,
      selectedTargetDates: []
    })
    
    this.generateBatchCalendar()
  },

  /**
   * 关闭批量操作模态框
   */
  onCloseBatchModal() {
    // 清理滚动提示定时器
    if (this.data.scrollIndicatorTimer) {
      clearTimeout(this.data.scrollIndicatorTimer)
    }

    // 开始出场动画
    this.setData({ batchModalVisible: false })

    // 等待动画完成后关闭模态框
    setTimeout(() => {
      this.setData({
        showBatchModal: false,
        selectedSourceDate: null,
        selectedTargetDates: [],
        sourceSchedulePreview: null,
        batchStep: 1,
        showScrollIndicator: false,
        scrollIndicatorTimer: null,
        hasShownScrollIndicator: false,
        batchCopyInProgress: false,
        batchCopyCompleted: false
      })
    }, 300) // 与CSS动画时长一致
  },

  /**
   * 选择模板日期（导入模式）
   */
  onSelectTemplate(e) {
    const { index } = e.currentTarget.dataset
    const templateItem = this.data.templateDates[index]
    
    if (!templateItem) return
    
    // 直接导入到当前选中的日期
    this.copyScheduleFromDate(templateItem.date, [this.data.selectedDate])
    
    this.onCloseBatchModal()
  },

  /**
   * 选择源日期（批量复制模式）
   */
  onSelectSourceDate(e) {
    const { index } = e.currentTarget.dataset
    const selectedTemplate = this.data.templateDates[index]
    
    this.setData({
      selectedSourceDate: selectedTemplate.date
    })
  },

  /**
   * 复制状态改变
   */
  onCopyStatusChange(e) {
    this.setData({
      copyStatus: e.detail.value
    })
  },

  /**
   * 复制收入改变
   */
  onCopyIncomeChange(e) {
    this.setData({
      copyIncome: e.detail.value
    })
  },

  /**
   * 开始日期改变
   */
  onStartDateChange(e) {
    this.setData({
      startDate: e.detail.value
    })
  },

  /**
   * 结束日期改变
   */
  onEndDateChange(e) {
    this.setData({
      endDate: e.detail.value
    })
  },

  /**
   * 确认批量操作
   */
  onConfirmBatchOperation() {
    if (this.data.batchOperation === 'import') {
      // 导入模式已经在onSelectTemplate中处理
      return
    }

    // 批量复制模式
    if (!this.data.selectedSourceDate) {
      wx.showToast({
        title: '请选择源日期',
        icon: 'none'
      })
      return
    }

    if (this.data.selectedTargetDates.length === 0) {
      wx.showToast({
        title: '请选择目标日期',
        icon: 'none'
      })
      return
    }

    // 防止重复点击
    if (this.data.batchCopyInProgress || this.data.batchCopyCompleted) {
      return
    }

    // 设置复制进行中状态
    this.setData({
      batchCopyInProgress: true
    })

    this.copyScheduleFromDate(this.data.selectedSourceDate, this.data.selectedTargetDates)

    // 显示复制完成状态
    setTimeout(() => {
      this.setData({
        batchCopyInProgress: false,
        batchCopyCompleted: true
      })

      // 2秒后自动关闭模态框
      setTimeout(() => {
        this.onCloseBatchModal()
      }, 2000)
    }, 1000)
  },

  /**
   * 复制工作安排到指定日期
   */
  copyScheduleFromDate(sourceDate, targetDates) {
    try {
      // 默认复制状态和收入信息
      this.timeSegmentService.copyScheduleToOtherDates(
        sourceDate,
        targetDates,
        true, // 始终复制状态
        true  // 始终复制收入信息
      )
      
      // 刷新数据
      this.loadCalendarData()
      this.loadSelectedDateData()
      this.updateStatistics()
      
      // 触发全局数据更新事件
      this.triggerDataUpdateEvent()
      
      wx.showToast({
        title: `已复制到${targetDates.length}个日期`,
        icon: 'success'
      })
    } catch (error) {
      console.error('复制失败:', error)
      wx.showToast({
        title: '复制失败',
        icon: 'none'
      })
    }
  },

  /**
   * 切换开始时间的次日状态
   */
  onToggleStartNextDay(e) {
    const { index } = e.currentTarget.dataset
    const timeInputs = this.data.timeInputs.slice()

    if (timeInputs[index]) {
      timeInputs[index].isStartNextDay = !timeInputs[index].isStartNextDay
      this.setData({ timeInputs }, () => {
        this.sortTimeInputsByStartTime()
        this.updateAllCalculations()
      })
    }
  },

  /**
   * 切换结束时间的次日状态
   */
  onToggleEndNextDay(e) {
    const { index } = e.currentTarget.dataset
    const timeInputs = this.data.timeInputs.slice()

    if (timeInputs[index]) {
      timeInputs[index].isEndNextDay = !timeInputs[index].isEndNextDay
      this.setData({ timeInputs }, () => {
        this.sortTimeInputsByStartTime()
        this.updateAllCalculations()
      })
    }
  },

  /**
   * 收入输入变化
   */
  onIncomeChange(e) {
    const { index } = e.currentTarget.dataset
    let { value } = e.detail
    const timeInputs = this.data.timeInputs.slice()

    if (timeInputs[index]) {
      // 限制小数位数最多2位
      value = this.limitDecimalPlaces(value, 2)

      // 处理空值情况，避免强制转换为0
      const numValue = value === '' ? 0 : (parseFloat(value) || 0)

      // 保存字符串值用于显示，数字值用于计算
      timeInputs[index].incomeText = value
      timeInputs[index].income = numValue
      timeInputs[index]._isEditingHourlyRate = false // 清除时薪编辑标记

      // 重新计算时薪（标记为收入变化触发的计算）
      if (timeInputs[index].type !== 'rest') {
        const duration = this.calculateInputDuration(timeInputs[index])
        if (duration > 0 && numValue > 0) {
          const hourlyRate = numValue / (duration / 60)
          timeInputs[index].hourlyRate = Math.round(hourlyRate * 100) / 100
          // 只有在时薪文本为空或者用户不在编辑时薪时才格式化
          if (!timeInputs[index].hourlyRateText || !timeInputs[index]._isEditingHourlyRate) {
            timeInputs[index].hourlyRateText = timeInputs[index].hourlyRate.toFixed(2)
          }
          timeInputs[index]._lastUpdatedBy = 'income' // 标记最后更新来源
        } else if (numValue === 0) {
          // 收入为0时，清空时薪（但不强制格式化）
          timeInputs[index].hourlyRate = 0
          if (!timeInputs[index]._isEditingHourlyRate) {
            timeInputs[index].hourlyRateText = ''
          }
          timeInputs[index]._lastUpdatedBy = 'income'
        }
      }

      this.setData({ timeInputs }, () => {
        this.updateAllCalculations()
      })
    }
  },

  /**
   * 时薪输入变化
   */
  onHourlyRateChange(e) {
    const { index } = e.currentTarget.dataset
    let { value } = e.detail
    const timeInputs = this.data.timeInputs.slice()

    if (timeInputs[index]) {
      // 限制小数位数最多2位
      value = this.limitDecimalPlaces(value, 2)

      // 处理空值情况，避免强制转换为0
      const numValue = value === '' ? 0 : (parseFloat(value) || 0)

      // 保存字符串值用于显示，数字值用于计算
      timeInputs[index].hourlyRateText = value
      timeInputs[index].hourlyRate = numValue
      timeInputs[index]._isEditingHourlyRate = true // 标记正在编辑时薪

      // 重新计算收入（标记为时薪变化触发的计算）
      if (timeInputs[index].type !== 'rest') {
        const duration = this.calculateInputDuration(timeInputs[index])
        if (duration > 0 && numValue > 0) {
          const income = numValue * (duration / 60)
          timeInputs[index].income = Math.round(income * 100) / 100
          timeInputs[index].incomeText = timeInputs[index].income.toString()
          timeInputs[index]._lastUpdatedBy = 'hourlyRate' // 标记最后更新来源
        } else if (numValue === 0) {
          // 时薪为0时，清空收入
          timeInputs[index].income = 0
          timeInputs[index].incomeText = ''
          timeInputs[index]._lastUpdatedBy = 'hourlyRate'
        }
      }

      this.setData({ timeInputs }, () => {
        this.updateAllCalculations()
      })
    }
  },

  /**
   * 时薪输入框失去焦点
   */
  onHourlyRateBlur(e) {
    const { index } = e.currentTarget.dataset
    const timeInputs = this.data.timeInputs.slice()

    if (timeInputs[index]) {
      // 清除编辑标记，允许重新格式化
      timeInputs[index]._isEditingHourlyRate = false
      this.setData({ timeInputs })
    }
  },

  /**
   * 限制小数位数
   */
  limitDecimalPlaces(value, maxDecimalPlaces) {
    if (!value || value === '') return ''

    // 移除非数字和小数点的字符
    value = value.replace(/[^\d.]/g, '')

    // 处理以小数点开头的情况：.123 -> 0.123
    if (value.startsWith('.')) {
      value = '0' + value
    }

    // 确保只有一个小数点
    const parts = value.split('.')
    if (parts.length > 2) {
      // 保留第一个小数点，移除后续的小数点
      value = parts[0] + '.' + parts.slice(1).join('').replace(/\./g, '')
    }

    // 重新分割以获取正确的部分
    const finalParts = value.split('.')

    // 处理前导零的情况
    if (finalParts[0].length > 1 && finalParts[0].startsWith('0')) {
      // 移除前导零，但保留单独的0（如0.123）
      finalParts[0] = finalParts[0].replace(/^0+/, '') || '0'
      value = finalParts.join('.')
    }

    // 限制小数位数
    if (finalParts.length === 2 && finalParts[1].length > maxDecimalPlaces) {
      const integerPart = finalParts[0]
      const decimalPart = finalParts[1].substring(0, maxDecimalPlaces)
      value = integerPart + '.' + decimalPart
    }

    return value
  },

  /**
   * 打开智能填写收入模态框
   */
  onSmartIncomeCalculate() {
    this.setData({
      showSmartIncomeModal: true
    })

    // 延迟显示动画
    setTimeout(() => {
      this.setData({ smartIncomeModalVisible: true })
    }, 50)
  },

  /**
   * 关闭智能填写收入模态框
   */
  onCloseSmartIncomeModal() {
    // 开始出场动画
    this.setData({ smartIncomeModalVisible: false })

    // 等待动画完成后关闭模态框
    setTimeout(() => {
      this.setData({
        showSmartIncomeModal: false
      })
    }, 300) // 与CSS动画时长一致
  },

  /**
   * 选择收入计算模式
   */
  onSelectIncomeMode(e) {
    const { mode } = e.currentTarget.dataset
    this.setData({
      smartIncomeMode: mode
    })
  },

  /**
   * 总收入输入变化
   */
  onSmartIncomeTotalChange(e) {
    let value = this.limitDecimalPlaces(e.detail.value, 2)
    const numValue = parseFloat(value) || 0

    this.setData({
      smartIncomeTotalAmount: numValue,
      smartIncomeTotalAmountText: value
    })
  },

  /**
   * 基础时薪输入变化
   */
  onSmartIncomeBaseHourlyChange(e) {
    let value = this.limitDecimalPlaces(e.detail.value, 2)
    const numValue = parseFloat(value) || 0

    this.setData({
      smartIncomeBaseHourly: numValue,
      smartIncomeBaseHourlyText: value
    })
  },

  /**
   * 加班倍率输入变化
   */
  onSmartIncomeOvertimeRateChange(e) {
    let value = this.limitDecimalPlaces(e.detail.value, 2)
    const numValue = parseFloat(value) || 1

    this.setData({
      smartIncomeOvertimeRate: numValue,
      smartIncomeOvertimeRateText: value
    })
  },

  /**
   * 选择加班倍率计算方式
   */
  onSelectSmartIncomeOvertimeCalculationMethod(e) {
    const { method } = e.currentTarget.dataset
    this.setData({
      overtimeCalculationMethod: method,
      smartIncomeOvertimeCalculationMethod: method
    })
  },

  /**
   * 加班倍率模式下的总收入输入变化
   */
  onSmartIncomeOvertimeTotalChange(e) {
    let value = this.limitDecimalPlaces(e.detail.value, 2)
    const numValue = parseFloat(value) || 0

    this.setData({
      smartIncomeOvertimeTotalAmount: numValue,
      smartIncomeOvertimeTotalAmountText: value
    })
  },

  /**
   * 工作时薪输入变化
   */
  onSmartIncomeWorkHourlyChange(e) {
    let value = this.limitDecimalPlaces(e.detail.value, 2)
    const numValue = parseFloat(value) || 0

    this.setData({
      smartIncomeWorkHourly: numValue,
      smartIncomeWorkHourlyText: value
    })
  },

  /**
   * 加班时薪输入变化
   */
  onSmartIncomeOvertimeHourlyChange(e) {
    let value = this.limitDecimalPlaces(e.detail.value, 2)
    const numValue = parseFloat(value) || 0

    this.setData({
      smartIncomeOvertimeHourly: numValue,
      smartIncomeOvertimeHourlyText: value
    })
  },

  /**
   * 确认智能填写收入
   */
  onConfirmSmartIncome() {
    const timeInputs = this.data.timeInputs.slice()

    try {
      switch (this.data.smartIncomeMode) {
        case 'total':
          this.calculateTotalIncomeDistribution(timeInputs)
          break
        case 'overtime':
          this.calculateOvertimeRateIncome(timeInputs)
          break
        case 'hourly':
          this.calculateHourlyRateIncome(timeInputs)
          break
      }

      this.setData({
        timeInputs
      }, () => {
        // 更新显示信息
        this.updateAllCalculations()
      })

      // 关闭模态框（带动画）
      this.onCloseSmartIncomeModal()

      wx.showToast({
        title: '收入计算完成',
        icon: 'success'
      })
    } catch (error) {
      console.error('收入计算失败:', error)
      wx.showToast({
        title: '计算失败',
        icon: 'none'
      })
    }
  },

  /**
   * 打开日收入计算器模态框
   */
  onOpenDailyIncomeCalculator() {
    // 获取当前工作的月薪作为默认值
    const currentMonthlySalary = this.getCurrentWorkMonthlySalary()

    this.setData({
      dailyIncomeCalculatorMonthlyIncome: currentMonthlySalary.toString(),
      dailyIncomeCalculatorTargetMode: 'total', // 标记目标模式
      showDailyIncomeCalculatorModal: true
    }, () => {
      // 计算初始结果
      this.calculateDailyIncomeResult()

      // 延迟显示动画
      setTimeout(() => {
        this.setData({ dailyIncomeCalculatorModalVisible: true })
      }, 50)
    })
  },

  /**
   * 关闭日收入计算器模态框
   */
  onCloseDailyIncomeCalculator() {
    // 开始出场动画
    this.setData({ dailyIncomeCalculatorModalVisible: false })

    // 等待动画完成后关闭模态框
    setTimeout(() => {
      this.setData({
        showDailyIncomeCalculatorModal: false,
        dailyIncomeCalculatorTargetMode: null
      })
    }, 300) // 与CSS动画时长一致
  },

  /**
   * 工作天数输入变化
   */
  onDailyIncomeWorkDaysChange(e) {
    let value = this.limitDecimalPlaces(e.detail.value, 2)
    const numValue = parseFloat(value) || 0

    if (numValue > 31) {
      wx.showToast({
        title: '工作天数不能超过31天',
        icon: 'none'
      })
      return
    }

    this.setData({
      dailyIncomeCalculatorWorkDays: value,
      dailyIncomeCalculatorWorkDaysText: value
    }, () => {
      this.calculateDailyIncomeResult()
    })
  },

  /**
   * 月收入输入变化
   */
  onDailyIncomeMonthlyIncomeChange(e) {
    let value = this.limitDecimalPlaces(e.detail.value, 2)
    const numValue = parseFloat(value) || 0

    if (numValue > 1000000000) {
      wx.showToast({
        title: '月收入不能超过1000,000,000元',
        icon: 'none'
      })
      return
    }

    this.setData({
      dailyIncomeCalculatorMonthlyIncome: value,
      dailyIncomeCalculatorMonthlyIncomeText: value
    }, () => {
      this.calculateDailyIncomeResult()
    })
  },

  /**
   * 获取当前工作的月薪
   */
  getCurrentWorkMonthlySalary() {
    try {
      const currentWork = this.workHistoryService.getCurrentWork()
      if (!currentWork) {
        return 10000 // 默认值
      }

      // 获取当前日期
      const currentDate = new Date()

      // 1. 当前工作履历设置了转正薪资
      if (currentWork.formalSalary && currentWork.formalSalary > 0) {
        // 1.1. 没有设置转正日期，直接采用转正薪资
        if (!currentWork.probationEndDate) {
          return currentWork.formalSalary
        }

        // 1.2. 设置了转正日期，判断是否转正
        const probationEndDate = new Date(currentWork.probationEndDate)

        // 1.2.1. 如果当前已经转正，则采用转正薪资
        if (currentDate > probationEndDate) {
          return currentWork.formalSalary
        }

        // 1.2.2. 如果当前没有转正，则采用试用薪资
        if (currentWork.probationSalary && currentWork.probationSalary > 0) {
          return currentWork.probationSalary
        }

        // 如果没有试用薪资，但有转正薪资，使用转正薪资
        return currentWork.formalSalary
      }

      // 2. 当前工作履历没有设置转正薪资，但是设置了试用薪资，则采用试用薪资
      if (currentWork.probationSalary && currentWork.probationSalary > 0) {
        return currentWork.probationSalary
      }

      // 3. 当前工作履历没有设置转正薪资和试用薪资，则采用默认值
      return 10000
    } catch (error) {
      console.error('获取当前工作月薪失败:', error)
      return 10000 // 默认值
    }
  },

  /**
   * 计算日收入结果
   */
  calculateDailyIncomeResult() {
    const workDays = parseFloat(this.data.dailyIncomeCalculatorWorkDays) || 0
    const monthlyIncome = parseFloat(this.data.dailyIncomeCalculatorMonthlyIncome) || 0

    let dailyIncome = 0
    if (workDays > 0) {
      dailyIncome = monthlyIncome / workDays
    }

    this.setData({
      dailyIncomeCalculatorResult: dailyIncome.toFixed(2)
    })
  },

  /**
   * 打开日收入计算器模态框（加班倍率模式）
   */
  onOpenDailyIncomeCalculatorForOvertime() {
    // 获取当前工作的月薪作为默认值
    const currentMonthlySalary = this.getCurrentWorkMonthlySalary()

    this.setData({
      dailyIncomeCalculatorMonthlyIncome: currentMonthlySalary.toString(),
      dailyIncomeCalculatorTargetMode: 'overtime', // 标记目标模式
      showDailyIncomeCalculatorModal: true
    }, () => {
      // 计算初始结果
      this.calculateDailyIncomeResult()
    })
  },

  /**
   * 确认使用计算的日收入
   */
  onConfirmDailyIncomeCalculator() {
    const calculatedDailyIncome = this.data.dailyIncomeCalculatorResult
    const targetMode = this.data.dailyIncomeCalculatorTargetMode || 'total'

    if (targetMode === 'overtime') {
      // 填入加班倍率模式的总收入
      this.setData({
        smartIncomeOvertimeTotalAmount: calculatedDailyIncome,
        showDailyIncomeCalculatorModal: false,
        dailyIncomeCalculatorTargetMode: null
      })
    } else {
      // 填入总收入分配模式的总收入
      this.setData({
        smartIncomeTotalAmount: calculatedDailyIncome,
        showDailyIncomeCalculatorModal: false,
        dailyIncomeCalculatorTargetMode: null
      })
    }

    wx.showToast({
      title: '日收入已填入',
      icon: 'success'
    })
  },

  /**
   * 按总收入分配计算
   */
  calculateTotalIncomeDistribution(timeInputs) {
    const totalAmount = parseFloat(this.data.smartIncomeTotalAmount) || 0
    if (totalAmount <= 0) {
      throw new Error('请输入有效的总收入')
    }

    // 计算总工作时间（排除休息）
    let totalWorkMinutes = 0
    timeInputs.forEach(input => {
      if (input.type !== 'rest') {
        const duration = this.calculateInputDuration(input)
        totalWorkMinutes += duration
      }
    })

    if (totalWorkMinutes <= 0) {
      throw new Error('没有工作时间段')
    }

    // 按时间比例分配收入
    timeInputs.forEach(input => {
      if (input.type !== 'rest') {
        const duration = this.calculateInputDuration(input)
        const ratio = duration / totalWorkMinutes
        input.income = parseFloat((totalAmount * ratio).toFixed(2))
        input.incomeText = input.income.toString()
        input.hourlyRate = parseFloat((input.income / (duration / 60)).toFixed(2))
        input.hourlyRateText = input.hourlyRate.toFixed(2)
        input._lastUpdatedBy = 'income' // 标记为基于收入计算
      } else {
        input.income = 0
        input.incomeText = ''
        input.hourlyRate = 0
        input.hourlyRateText = ''
      }
    })
  },

  /**
   * 按加班倍率计算
   */
  calculateOvertimeRateIncome(timeInputs) {
    const overtimeRate = parseFloat(this.data.smartIncomeOvertimeRate) || 1.5
    const calculationMethod = this.data.smartIncomeOvertimeCalculationMethod

    if (calculationMethod === 'hourly') {
      // 基础时薪方式
      this.calculateOvertimeRateByHourly(timeInputs, overtimeRate)
    } else {
      // 总收入方式
      this.calculateOvertimeRateByTotal(timeInputs, overtimeRate)
    }
  },

  /**
   * 基础时薪方式计算加班倍率
   */
  calculateOvertimeRateByHourly(timeInputs, overtimeRate) {
    const baseHourly = parseFloat(this.data.smartIncomeBaseHourly) || 0

    if (baseHourly <= 0) {
      throw new Error('请输入有效的基础时薪')
    }

    timeInputs.forEach(input => {
      if (input.type === 'rest') {
        input.income = 0
        input.hourlyRate = 0
        input.hourlyRateText = ''
      } else {
        const duration = this.calculateInputDuration(input)
        const hours = duration / 60

        if (input.type === 'overtime') {
          input.hourlyRate = parseFloat((baseHourly * overtimeRate).toFixed(2))
        } else {
          input.hourlyRate = parseFloat(baseHourly.toFixed(2))
        }

        input.income = parseFloat((hours * input.hourlyRate).toFixed(2))
        input.incomeText = input.income.toString()
        input.hourlyRateText = input.hourlyRate.toFixed(2)
        input._lastUpdatedBy = 'hourlyRate' // 标记为基于时薪计算
      }
    })
  },

  /**
   * 总收入方式计算加班倍率
   */
  calculateOvertimeRateByTotal(timeInputs, overtimeRate) {
    const totalAmount = parseFloat(this.data.smartIncomeOvertimeTotalAmount) || 0

    if (totalAmount <= 0) {
      throw new Error('请输入有效的总收入')
    }

    // 计算总工作时间和加班时间
    let totalWorkMinutes = 0
    let totalOvertimeMinutes = 0
    let totalNormalMinutes = 0

    timeInputs.forEach(input => {
      if (input.type !== 'rest') {
        const duration = this.calculateInputDuration(input)
        totalWorkMinutes += duration

        if (input.type === 'overtime') {
          totalOvertimeMinutes += duration
        } else {
          totalNormalMinutes += duration
        }
      }
    })

    if (totalWorkMinutes === 0) {
      throw new Error('没有工作时间段')
    }

    // 根据加班倍率计算基础时薪
    // 设基础时薪为 x，则：
    // 总收入 = 正常工作时间 * x + 加班时间 * x * 倍率
    // 总收入 = x * (正常工作时间 + 加班时间 * 倍率)
    // x = 总收入 / (正常工作时间 + 加班时间 * 倍率)

    const normalHours = totalNormalMinutes / 60
    const overtimeHours = totalOvertimeMinutes / 60
    const weightedTotalHours = normalHours + (overtimeHours * overtimeRate)

    if (weightedTotalHours === 0) {
      throw new Error('计算出的加权工作时间为0')
    }

    const baseHourlyRate = totalAmount / weightedTotalHours

    // 为每个时间段分配收入
    timeInputs.forEach(input => {
      if (input.type === 'rest') {
        input.income = 0
        input.hourlyRate = 0
        input.hourlyRateText = ''
      } else {
        const duration = this.calculateInputDuration(input)
        const hours = duration / 60

        if (input.type === 'overtime') {
          input.hourlyRate = parseFloat((baseHourlyRate * overtimeRate).toFixed(2))
        } else {
          input.hourlyRate = parseFloat(baseHourlyRate.toFixed(2))
        }

        input.income = Math.round(hours * input.hourlyRate * 100) / 100
        input.incomeText = input.income.toString()
        input.hourlyRateText = input.hourlyRate.toFixed(2)
        input._lastUpdatedBy = 'income' // 标记为基于收入计算
      }
    })

    console.log('总收入方式计算结果:', {
      totalAmount,
      normalHours: normalHours.toFixed(2),
      overtimeHours: overtimeHours.toFixed(2),
      overtimeRate,
      weightedTotalHours: weightedTotalHours.toFixed(2),
      baseHourlyRate: baseHourlyRate.toFixed(2)
    })
  },

  /**
   * 按分类时薪计算
   */
  calculateHourlyRateIncome(timeInputs) {
    const workHourly = parseFloat(this.data.smartIncomeWorkHourly) || 0
    const overtimeHourly = parseFloat(this.data.smartIncomeOvertimeHourly) || 0

    if (workHourly <= 0 && overtimeHourly <= 0) {
      throw new Error('请至少输入一种时薪')
    }

    timeInputs.forEach(input => {
      if (input.type === 'rest') {
        input.income = 0
        input.hourlyRate = 0
        input.hourlyRateText = ''
      } else {
        const duration = this.calculateInputDuration(input)
        const hours = duration / 60

        if (input.type === 'overtime') {
          input.hourlyRate = parseFloat(overtimeHourly.toFixed(2))
        } else {
          input.hourlyRate = parseFloat(workHourly.toFixed(2))
        }

        input.income = parseFloat((hours * input.hourlyRate).toFixed(2))
        input.incomeText = input.income.toString()
        input.hourlyRateText = input.hourlyRate.toFixed(2)
        input._lastUpdatedBy = 'hourlyRate' // 标记为基于时薪计算
      }
    })
  },

  /**
   * 计算时间输入的持续时间（分钟）
   */
  calculateInputDuration(input) {
    if (!input.startTime || !input.endTime) return 0

    const [startHour, startMinute] = input.startTime.split(':').map(Number)
    const [endHour, endMinute] = input.endTime.split(':').map(Number)

    let startMinutes = startHour * 60 + startMinute
    let endMinutes = endHour * 60 + endMinute

    // 处理次日标记
    if (input.isStartNextDay) startMinutes += 24 * 60
    if (input.isEndNextDay) endMinutes += 24 * 60

    // 如果结束时间小于等于开始时间，且没有明确的次日标记，返回0（无效）
    if (endMinutes <= startMinutes && !input.isEndNextDay && !input.isStartNextDay) {
      return 0
    }

    return Math.max(0, endMinutes - startMinutes)
  },

  /**
   * 检测时间段问题（包括冲突、无效时间等）
   */
  checkTimeConflicts() {
    const timeInputs = this.data.timeInputs
    let hasAnyIssue = false

    // 重置所有时间段的问题状态
    timeInputs.forEach(input => {
      input.hasConflict = false
      input.warningMessage = ''
      input.warningType = ''
    })

    // 检查每个时间段的有效性
    timeInputs.forEach((input, index) => {
      const issues = this.validateTimeSegment(input, index)
      if (issues.length > 0) {
        input.hasConflict = true
        input.warningMessage = issues[0].message // 显示第一个问题
        input.warningType = issues[0].type
        hasAnyIssue = true
      }
    })

    // 检查时间段重叠
    for (let i = 0; i < timeInputs.length; i++) {
      for (let j = i + 1; j < timeInputs.length; j++) {
        if (this.isTimeRangeOverlap(timeInputs[i], timeInputs[j])) {
          timeInputs[i].hasConflict = true
          timeInputs[j].hasConflict = true
          timeInputs[i].warningMessage = `与第${j + 1}个时间段重叠`
          timeInputs[j].warningMessage = `与第${i + 1}个时间段重叠`
          timeInputs[i].warningType = 'overlap'
          timeInputs[j].warningType = 'overlap'
          hasAnyIssue = true
        }
      }
    }

    this.setData({
      timeInputs: timeInputs.slice(),
      hasTimeConflict: hasAnyIssue
    })

    return hasAnyIssue
  },

  /**
   * 验证单个时间段
   */
  validateTimeSegment(input) {
    const issues = []

    // 检查时间是否填写完整
    if (!input.startTime || !input.endTime) {
      issues.push({
        type: 'incomplete',
        message: '请填写完整的时间'
      })
      return issues
    }

    // 解析时间
    const [startHour, startMinute] = input.startTime.split(':').map(Number)
    const [endHour, endMinute] = input.endTime.split(':').map(Number)

    let startMinutes = startHour * 60 + startMinute
    let endMinutes = endHour * 60 + endMinute

    // 处理次日标记
    if (input.isStartNextDay) startMinutes += 24 * 60
    if (input.isEndNextDay) endMinutes += 24 * 60

    // 检查开始时间是否等于结束时间
    if (startMinutes === endMinutes) {
      issues.push({
        type: 'same_time',
        message: '开始时间和结束时间不能相同'
      })
      return issues
    }

    // 检查结束时间是否早于开始时间（且没有正确标记次日）
    if (endMinutes < startMinutes) {
      // 如果结束时间早于开始时间，但没有标记结束时间为次日，这是错误的
      if (!input.isEndNextDay && !input.isStartNextDay) {
        issues.push({
          type: 'end_before_start',
          message: '结束时间早于开始时间，请勾选"次日"'
        })
        return issues
      }
    }

    // 计算实际持续时间
    const duration = this.calculateInputDuration(input)

    // 检查时间段是否过短（小于1分钟）
    if (duration < 1) {
      issues.push({
        type: 'too_short',
        message: '时间段无效'
      })
    }

    return issues
  },

  /**
   * 检查两个时间段是否重叠
   */
  isTimeRangeOverlap(input1, input2) {
    const start1 = timeStringToMinutes(input1.startTime, input1.isStartNextDay)
    const end1 = timeStringToMinutes(input1.endTime, input1.isEndNextDay)
    const start2 = timeStringToMinutes(input2.startTime, input2.isStartNextDay)
    const end2 = timeStringToMinutes(input2.endTime, input2.isEndNextDay)

    // 处理传统跨日逻辑
    let actualEnd1 = end1
    let actualEnd2 = end2

    if (!input1.isStartNextDay && !input1.isEndNextDay && end1 <= start1) {
      actualEnd1 = end1 + 24 * 60
    }

    if (!input2.isStartNextDay && !input2.isEndNextDay && end2 <= start2) {
      actualEnd2 = end2 + 24 * 60
    }

    // 检查是否有重叠（不包括边界相等的情况）
    return start1 < actualEnd2 && start2 < actualEnd1
  },

  /**
   * 验证时间输入的合法性
   */
  validateTimeInputs(timeInputs) {
    for (let i = 0; i < timeInputs.length; i++) {
      const input = timeInputs[i]

      // 检查时间是否填写完整
      if (!input.startTime || !input.endTime) {
        return {
          isValid: false,
          message: `第${i + 1}个时间段的时间未填写完整`
        }
      }

      // 计算时间段持续时间
      const duration = this.calculateInputDuration(input)

      // 检查时间段持续时间是否大于0
      if (duration <= 0) {
        return {
          isValid: false,
          message: `第${i + 1}个时间段的结束时间必须晚于开始时间`
        }
      }

      // 检查时间段持续时间是否过短（小于1分钟）
      if (duration < 1) {
        return {
          isValid: false,
          message: `第${i + 1}个时间段的持续时间不能少于1分钟`
        }
      }

      // 检查时间段持续时间是否过长（超过48小时）
      if (duration > 48 * 60) {
        return {
          isValid: false,
          message: `第${i + 1}个时间段的持续时间不能超过48小时`
        }
      }

      // 检查收入数据的合法性
      if (input.type !== 'rest') {
        if (input.income < 0) {
          return {
            isValid: false,
            message: `第${i + 1}个时间段的收入不能为负数`
          }
        }

        // 检查收入是否超过合理范围（例如单个时间段不超过10万元）
        if (input.income > 1000000000) {
          return {
            isValid: false,
            message: `第${i + 1}个时间段的收入不能超过1000,000,000元`
          }
        }
      }
    }

    return {
      isValid: true,
      message: ''
    }
  },

  /**
   * 处理收入数据，确保小数位数不超过2位
   */
  processIncomeData(timeInputs) {
    return timeInputs.map(input => {
      const processedInput = { ...input }

      if (input.type !== 'rest' && typeof input.income === 'number') {
        // 限制收入最多保留2位小数
        processedInput.income = Math.round(input.income * 100) / 100

        // 重新计算时薪
        const duration = this.calculateInputDuration(input)
        if (duration > 0) {
          const hourlyRate = processedInput.income / (duration / 60)
          processedInput.hourlyRate = Math.round(hourlyRate * 100) / 100
          processedInput.hourlyRateText = processedInput.hourlyRate.toFixed(2)
          processedInput._lastUpdatedBy = 'income' // 标记为基于收入计算
        }
      }

      return processedInput
    })
  },

  /**
   * 计算总收入
   */
  calculateTotalIncome() {
    const timeInputs = this.data.timeInputs
    let total = 0

    timeInputs.forEach(input => {
      if (input.type !== 'rest' && input.income) {
        total += parseFloat(input.income) || 0
      }
    })

    this.setData({
      totalIncome: total,
      totalIncomeText: total.toFixed(2)
    })

    return total
  },

  /**
   * 计算时间统计
   */
  calculateTimeStatistics() {
    const timeInputs = this.data.timeInputs
    let workMinutes = 0
    let restMinutes = 0
    let overtimeMinutes = 0

    timeInputs.forEach((input) => {
      const duration = this.calculateInputDuration(input)

      if (duration > 0) {
        switch (input.type) {
          case 'work':
            workMinutes += duration
            break
          case 'rest':
            restMinutes += duration
            break
          case 'overtime':
            overtimeMinutes += duration
            break
        }
      }
    })

    // 转换为小时并格式化显示
    const formatHours = (minutes) => {
      const hours = Math.floor(minutes / 60)
      const remainingMinutes = minutes % 60

      if (hours > 0 && remainingMinutes > 0) {
        return `${hours}小时${remainingMinutes}分钟`
      } else if (hours > 0) {
        return `${hours}小时`
      } else if (remainingMinutes > 0) {
        return `${remainingMinutes}分钟`
      } else {
        return '0小时'
      }
    }

    this.setData({
      workHours: workMinutes / 60,
      workHoursText: formatHours(workMinutes),
      restHours: restMinutes / 60,
      restHoursText: formatHours(restMinutes),
      overtimeHours: overtimeMinutes / 60,
      overtimeHoursText: formatHours(overtimeMinutes)
    })
  },

  /**
   * 按开始时间排序时间段
   */
  sortTimeInputsByStartTime() {
    const timeInputs = this.data.timeInputs.slice()

    timeInputs.sort((a, b) => {
      const timeA = timeStringToMinutes(a.startTime, a.isStartNextDay)
      const timeB = timeStringToMinutes(b.startTime, b.isStartNextDay)
      return timeA - timeB
    })

    this.setData({
      timeInputs
    })
  },

  /**
   * 计算合理的默认时薪
   */
  calculateReasonableHourlyRate(existingTimeInputs) {
    // 1. 优先使用现有时间段的平均时薪
    const workSegments = existingTimeInputs.filter(input =>
      (input.type === 'work' || input.type === 'overtime') && input.hourlyRate > 0
    )

    if (workSegments.length > 0) {
      const totalHourlyRate = workSegments.reduce((sum, input) => sum + input.hourlyRate, 0)
      return Math.round((totalHourlyRate / workSegments.length) * 100) / 100
    }

    // 2. 如果没有现有时薪，使用日收入目标计算
    const dailyIncome = this.data.dailyIncome || 500
    const estimatedWorkHours = 8 // 假设一天工作8小时
    const estimatedHourlyRate = dailyIncome / estimatedWorkHours

    return Math.round(estimatedHourlyRate * 100) / 100
  },

  /**
   * 创建带有合理收入分配的默认时间段
   */
  createDefaultTimeInputsWithIncome(targetDailyIncome) {
    // 定义默认时间段结构
    const defaultSegments = [
      {
        startTime: '09:00',
        endTime: '12:00',
        type: 'work',
        typeIndex: 0
      },
      {
        startTime: '12:00',
        endTime: '14:00',
        type: 'rest',
        typeIndex: 1
      },
      {
        startTime: '14:00',
        endTime: '18:00',
        type: 'work',
        typeIndex: 0
      }
    ]

    // 计算总工作时长（分钟）
    let totalWorkMinutes = 0
    defaultSegments.forEach(segment => {
      if (segment.type === 'work') {
        const [startHour, startMinute] = segment.startTime.split(':').map(Number)
        const [endHour, endMinute] = segment.endTime.split(':').map(Number)
        const duration = (endHour * 60 + endMinute) - (startHour * 60 + startMinute)
        totalWorkMinutes += duration
      }
    })

    // 计算平均时薪
    const averageHourlyRate = totalWorkMinutes > 0 ? (targetDailyIncome / (totalWorkMinutes / 60)) : 60

    // 创建时间段对象并分配收入
    return defaultSegments.map(segment => {
      const timeInput = {
        startTime: segment.startTime,
        endTime: segment.endTime,
        type: segment.type,
        typeIndex: segment.typeIndex,
        isStartNextDay: false,
        isEndNextDay: false,
        _lastUpdatedBy: 'income' // 标记为基于收入计算的
      }

      if (segment.type === 'work') {
        // 计算这个工作时间段的持续时间
        const [startHour, startMinute] = segment.startTime.split(':').map(Number)
        const [endHour, endMinute] = segment.endTime.split(':').map(Number)
        const duration = (endHour * 60 + endMinute) - (startHour * 60 + startMinute)

        // 按比例分配收入
        const segmentIncome = Math.round((duration / totalWorkMinutes) * targetDailyIncome * 100) / 100

        timeInput.income = segmentIncome
        timeInput.incomeText = segmentIncome.toString()
        timeInput.hourlyRate = Math.round(averageHourlyRate * 100) / 100
        timeInput.hourlyRateText = timeInput.hourlyRate.toFixed(2)
      } else {
        // 休息时间段
        timeInput.income = 0
        timeInput.incomeText = ''
        timeInput.hourlyRate = 0
        timeInput.hourlyRateText = '0.00'
      }

      return timeInput
    })
  },

  /**
   * 更新所有计算（统一方法）
   */
  updateAllCalculations() {
    this.updateTimeInputsDisplay()
    this.calculateTotalIncome()
    this.calculateTimeStatistics()
    this.checkTimeConflicts()
  },

  /**
   * 更新时间段的持续时间和时薪显示
   */
  updateTimeInputsDisplay() {
    const timeInputs = this.data.timeInputs.slice()

    timeInputs.forEach(input => {
      // 计算持续时间
      const duration = this.calculateInputDuration(input)
      const hours = Math.floor(duration / 60)
      const minutes = duration % 60

      if (hours > 0 && minutes > 0) {
        input.durationText = `${hours}小时${minutes}分钟`
      } else if (hours > 0) {
        input.durationText = `${hours}小时`
      } else {
        input.durationText = `${minutes}分钟`
      }

      // 重新计算收入和时薪（基于最后编辑的字段）
      if (input.type !== 'rest' && duration > 0) {
        // 如果有收入和时薪，需要根据最后编辑的字段重新计算
        if (input.income > 0 || input.hourlyRate > 0) {
          // 如果最后编辑的是时薪，或者只有时薪没有收入，则根据时薪计算收入
          if (input._lastUpdatedBy === 'hourlyRate' || (input.hourlyRate > 0 && !input.income)) {
            const income = input.hourlyRate * (duration / 60)
            input.income = Math.round(income * 100) / 100
            input.incomeText = input.income.toString()
            // 不重新格式化时薪文本，保持用户输入的格式
            if (!input.hourlyRateText || !input._isEditingHourlyRate) {
              input.hourlyRateText = input.hourlyRate.toFixed(2)
            }
          }
          // 否则根据收入计算时薪（默认行为或最后编辑的是收入）
          else if (input.income > 0) {
            input.hourlyRate = parseFloat(((parseFloat(input.income) || 0) / (duration / 60)).toFixed(2))
            // 只有在用户不在编辑时薪时才重新格式化
            if (!input._isEditingHourlyRate) {
              input.hourlyRateText = input.hourlyRate.toFixed(2)
            }
            // 如果没有明确的更新来源，标记为收入更新
            if (!input._lastUpdatedBy) {
              input._lastUpdatedBy = 'income'
            }
          }
        }
        // 如果既没有收入也没有时薪，保持空值状态
        else {
          input.hourlyRate = 0
          // 不强制设置hourlyRateText，保持用户输入状态
          if (!input.hourlyRateText) {
            input.hourlyRateText = ''
          }
          input.income = 0
          // 不强制设置incomeText，保持用户输入状态
          if (!input.incomeText) {
            input.incomeText = ''
          }
        }
      } else if (input.type === 'rest') {
        // 休息时间段清空收入和时薪
        input.hourlyRate = 0
        input.hourlyRateText = ''
        input.income = 0
        input.incomeText = ''
        input._lastUpdatedBy = undefined
      }
    })

    this.setData({
      timeInputs
    })
  },

  // ==================== 摸鱼管理方法 ====================

  /**
   * 添加摸鱼记录
   */
  onAddFishing() {
    this.setData({
      showFishingEditor: true,
      fishingEditorMode: 'add',
      editingFishing: null
    })
  },

  /**
   * 编辑摸鱼记录
   */
  onEditFishing(e) {
    const fishingId = e.currentTarget.dataset.id
    const fishing = this.data.displayFishes.find(f => f.id === fishingId)

    if (!fishing) {
      wx.showToast({
        title: '摸鱼记录不存在',
        icon: 'error'
      })
      return
    }

    this.setData({
      showFishingEditor: true,
      fishingEditorMode: 'edit',
      editingFishing: fishing
    })
  },

  /**
   * 删除摸鱼记录
   */
  onDeleteFishing(e) {
    const fishingId = e.currentTarget.dataset.id
    const fishing = this.data.displayFishes.find(f => f.id === fishingId)

    if (!fishing) {
      wx.showToast({
        title: '摸鱼记录不存在',
        icon: 'error'
      })
      return
    }

    wx.showModal({
      title: '确认删除',
      content: `确定要删除这条摸鱼记录吗？\n时间：${fishing.startTime} - ${fishing.endTime}`,
      success: (res) => {
        if (res.confirm) {
          this.deleteFishingRecord(fishingId)
        }
      }
    })
  },

  /**
   * 摸鱼编辑器保存事件
   */
  onFishingEditorSave(e) {
    const { mode, fishingData } = e.detail

    try {
      if (mode === 'add') {
        this.addFishingRecord(fishingData)
      } else if (mode === 'edit') {
        this.updateFishingRecord(fishingData.id, fishingData)
      }

      // 关闭编辑器
      this.setData({
        showFishingEditor: false,
        editingFishing: null
      })
    } catch (error) {
      console.error('保存摸鱼记录失败:', error)
      wx.showToast({
        title: '保存失败',
        icon: 'error'
      })
    }
  },

  /**
   * 摸鱼编辑器删除事件
   */
  onFishingEditorDelete(e) {
    const { fishingData } = e.detail

    try {
      // 删除摸鱼记录
      this.deleteFishingRecord(fishingData.id)

      // 关闭编辑器
      this.setData({
        showFishingEditor: false,
        editingFishing: null
      })

      wx.showToast({
        title: '删除成功',
        icon: 'success'
      })
    } catch (error) {
      console.error('删除摸鱼记录失败:', error)
      wx.showToast({
        title: '删除失败',
        icon: 'error'
      })
    }
  },

  /**
   * 摸鱼编辑器取消事件
   */
  onFishingEditorCancel() {
    this.setData({
      showFishingEditor: false,
      editingFishing: null
    })
  },

  /**
   * 摸鱼编辑器关闭事件
   */
  onFishingEditorClose() {
    this.setData({
      showFishingEditor: false,
      editingFishing: null
    })
  },

  /**
   * 添加摸鱼记录
   */
  addFishingRecord(fishingData) {
    try {
      const dataManager = require('../../core/managers/data-manager.js')
      dataManager.addFishing(this.data.currentWorkId, this.data.selectedDate, fishingData)

      // 重新加载数据
      this.loadSelectedDateData()

      wx.showToast({
        title: '添加成功',
        icon: 'success'
      })
    } catch (error) {
      console.error('添加摸鱼记录失败:', error)
      throw error
    }
  },

  /**
   * 更新摸鱼记录
   */
  updateFishingRecord(fishingId, updateData) {
    try {
      const dataManager = require('../../core/managers/data-manager.js')
      dataManager.updateFishing(this.data.currentWorkId, this.data.selectedDate, fishingId, updateData)

      // 重新加载数据
      this.loadSelectedDateData()

      wx.showToast({
        title: '更新成功',
        icon: 'success'
      })
    } catch (error) {
      console.error('更新摸鱼记录失败:', error)
      wx.showToast({
        title: '更新失败',
        icon: 'error'
      })
    }
  },

  /**
   * 删除摸鱼记录
   */
  deleteFishingRecord(fishingId) {
    try {
      const dataManager = require('../../core/managers/data-manager.js')
      dataManager.deleteFishing(this.data.currentWorkId, this.data.selectedDate, fishingId)

      // 重新加载数据
      this.loadSelectedDateData()

      wx.showToast({
        title: '删除成功',
        icon: 'success'
      })
    } catch (error) {
      console.error('删除摸鱼记录失败:', error)
      wx.showToast({
        title: '删除失败',
        icon: 'error'
      })
    }
  },

  /**
   * 处理摸鱼数据冲突
   */
  handleFishingConflicts(result) {
    const conflictMessages = result.conflicts.map(conflict => {
      let message = `• ${conflict.fishingTime}`
      if (conflict.fishingRemark) {
        message += ` (${conflict.fishingRemark})`
      }
      message += `\n  ${conflict.reason}`
      return message
    }).join('\n\n')

    const content = `时间段修改会导致以下摸鱼记录冲突：\n\n${conflictMessages}\n\n您可以：\n1. 取消修改，保持原有时间段设置\n2. 删除冲突的摸鱼记录并保存时间段\n3. 手动调整摸鱼记录时间后再保存`

    wx.showActionSheet({
      itemList: ['取消修改', '删除冲突记录', '手动调整摸鱼记录'],
      success: (res) => {
        if (res.tapIndex === 1) {
          // 删除冲突记录
          this.deleteConflictingFishingRecords(result.conflicts)
        } else if (res.tapIndex === 2) {
          // 手动调整摸鱼记录
          this.showFishingConflictEditor(result.conflicts)
        }
        // tapIndex === 0 或取消，不做任何操作
      }
    })
  },

  /**
   * 显示摸鱼冲突编辑器
   */
  showFishingConflictEditor(conflicts) {
    if (conflicts.length === 0) return

    // 显示第一个冲突的摸鱼记录编辑器
    const firstConflict = conflicts[0]
    const fishing = this.data.displayFishes.find(f => f.id === firstConflict.fishingId)

    if (fishing) {
      wx.showToast({
        title: `请调整摸鱼记录时间，还有${conflicts.length}条冲突`,
        icon: 'none',
        duration: 2000
      })

      this.setData({
        showFishingEditor: true,
        fishingEditorMode: 'edit',
        editingFishing: fishing
      })
    }
  },

  /**
   * 删除冲突的摸鱼记录
   */
  deleteConflictingFishingRecords(conflicts) {
    try {
      const dataManager = require('../../core/managers/data-manager.js')

      // 删除所有冲突的摸鱼记录
      conflicts.forEach(conflict => {
        dataManager.deleteFishing(this.data.currentWorkId, this.data.selectedDate, conflict.fishingId)
      })

      // 先保存日期状态
      this.timeSegmentService.setDateStatus(this.data.selectedDate, this.data.dateStatus, this.data.currentWorkId)

      // 重新尝试保存时间段
      const processedTimeInputs = this.processIncomeData(this.data.timeInputs)
      this.timeSegmentService.setDaySchedule(
        this.data.selectedDate,
        processedTimeInputs,
        this.data.dailyIncome,
        this.data.currentWorkId
      )

      // 刷新数据
      this.loadCalendarData()
      this.loadSelectedDateData()
      this.updateStatistics()
      this.updateTimeStatistics()

      // 触发全局数据更新事件
      this.triggerDataUpdateEvent()

      // 关闭模态框
      this.onCloseScheduleModal()

      wx.showToast({
        title: `已删除${conflicts.length}条冲突记录并保存`,
        icon: 'success',
        duration: 3000
      })
    } catch (error) {
      console.error('删除冲突摸鱼记录失败:', error)
      wx.showToast({
        title: '处理冲突失败',
        icon: 'error'
      })
    }
  },

  // ==================== 收入调整相关方法 ====================

  /**
   * 加载选中日期的收入调整数据
   */
  loadSelectedDateAdjustmentData() {
    if (!this.data.selectedDate || !this.data.currentWorkId) {
      return null
    }

    try {
      console.log('开始加载收入调整数据，日期:', this.data.selectedDate)
      const adjustmentSummary = incomeAdjustmentService.getDayAdjustmentSummary(
        this.data.selectedDate,
        this.data.currentWorkId
      )
      console.log('获取到的收入调整汇总:', adjustmentSummary)

      // 验证数据结构
      if (!adjustmentSummary) {
        console.error('收入调整汇总为空')
        return null
      }

      // 为额外收入项目添加类型文本和确保数据格式
      if (adjustmentSummary.extraIncomeItems && Array.isArray(adjustmentSummary.extraIncomeItems)) {
        console.log('处理额外收入项目，数量:', adjustmentSummary.extraIncomeItems.length)
        adjustmentSummary.extraIncomeItems = adjustmentSummary.extraIncomeItems.map(item => ({
          ...item,
          amount: Number(item.amount) || 0,  // 确保 amount 是数字
          typeText: item.type || '未设置类型'  // 直接使用用户输入的类型
        }))
      } else {
        console.log('额外收入项目为空或不是数组:', adjustmentSummary.extraIncomeItems)
        adjustmentSummary.extraIncomeItems = []
      }

      // 为扣款项目添加类型文本和确保数据格式
      if (adjustmentSummary.deductionItems && Array.isArray(adjustmentSummary.deductionItems)) {
        console.log('处理扣款项目，数量:', adjustmentSummary.deductionItems.length)
        adjustmentSummary.deductionItems = adjustmentSummary.deductionItems.map(item => ({
          ...item,
          amount: Number(item.amount) || 0,  // 确保 amount 是数字
          typeText: item.type || '未设置类型'  // 直接使用用户输入的类型
        }))
      } else {
        console.log('扣款项目为空或不是数组:', adjustmentSummary.deductionItems)
        adjustmentSummary.deductionItems = []
      }

      console.log('处理后的收入调整数据:', adjustmentSummary)

      // 调试：检查描述字段
      if (adjustmentSummary.extraIncomeItems && adjustmentSummary.extraIncomeItems.length > 0) {
        console.log('额外收入项目详情:', adjustmentSummary.extraIncomeItems.map(item => ({
          type: item.type,
          desc: item.desc,
          typeText: item.typeText,
          amount: item.amount
        })))
      }

      if (adjustmentSummary.deductionItems && adjustmentSummary.deductionItems.length > 0) {
        console.log('扣款项目详情:', adjustmentSummary.deductionItems.map(item => ({
          type: item.type,
          desc: item.desc,
          typeText: item.typeText,
          amount: item.amount
        })))
      }

      return adjustmentSummary

    } catch (error) {
      console.error('加载收入调整数据失败:', error)
      return null
    }
  },

  /**
   * 将Date对象转换为YYYY-MM-DD格式的字符串
   */
  formatDateString(date) {
    if (!date || !(date instanceof Date)) {
      return ''
    }

    return formatDateKey(date)
  },

  /**
   * 添加额外收入
   */
  onAddExtraIncome() {
    const selectedDate = this.data.selectedDate
    console.log('用户点击添加额外收入，当前选中日期:', selectedDate)

    // 确保有选中的日期
    if (!selectedDate) {
      wx.showToast({
        title: '请先选择日期',
        icon: 'error'
      })
      return
    }

    // 转换为日期字符串
    const dateString = this.formatDateString(selectedDate)
    console.log('转换后的日期字符串:', dateString)

    if (!dateString) {
      wx.showToast({
        title: '日期格式错误',
        icon: 'error'
      })
      return
    }

    this.setData({
      showIncomeAdjustmentModal: true,
      adjustmentModalMode: 'income',
      adjustmentModalIsEdit: false,
      adjustmentModalEditItem: null,
      adjustmentModalDateString: dateString
    })
  },

  /**
   * 添加扣款
   */
  onAddDeduction() {
    const selectedDate = this.data.selectedDate
    console.log('用户点击添加扣款，当前选中日期:', selectedDate)

    // 确保有选中的日期
    if (!selectedDate) {
      wx.showToast({
        title: '请先选择日期',
        icon: 'error'
      })
      return
    }

    // 转换为日期字符串
    const dateString = this.formatDateString(selectedDate)
    console.log('转换后的日期字符串:', dateString)

    if (!dateString) {
      wx.showToast({
        title: '日期格式错误',
        icon: 'error'
      })
      return
    }

    this.setData({
      showIncomeAdjustmentModal: true,
      adjustmentModalMode: 'deduction',
      adjustmentModalIsEdit: false,
      adjustmentModalEditItem: null,
      adjustmentModalDateString: dateString
    })
  },

  /**
   * 编辑额外收入
   */
  onEditExtraIncome(e) {
    const item = e.currentTarget.dataset.item
    const selectedDate = this.data.selectedDate
    console.log('用户点击编辑额外收入:', item, '选中日期:', selectedDate)

    if (!selectedDate) {
      wx.showToast({
        title: '日期错误',
        icon: 'error'
      })
      return
    }

    const dateString = this.formatDateString(selectedDate)
    if (!dateString) {
      wx.showToast({
        title: '日期格式错误',
        icon: 'error'
      })
      return
    }

    this.setData({
      showIncomeAdjustmentModal: true,
      adjustmentModalMode: 'income',
      adjustmentModalIsEdit: true,
      adjustmentModalEditItem: item,
      adjustmentModalDateString: dateString
    })
  },

  /**
   * 编辑扣款
   */
  onEditDeduction(e) {
    const item = e.currentTarget.dataset.item
    const selectedDate = this.data.selectedDate
    console.log('用户点击编辑扣款:', item, '选中日期:', selectedDate)

    if (!selectedDate) {
      wx.showToast({
        title: '日期错误',
        icon: 'error'
      })
      return
    }

    const dateString = this.formatDateString(selectedDate)
    if (!dateString) {
      wx.showToast({
        title: '日期格式错误',
        icon: 'error'
      })
      return
    }

    this.setData({
      showIncomeAdjustmentModal: true,
      adjustmentModalMode: 'deduction',
      adjustmentModalIsEdit: true,
      adjustmentModalEditItem: item,
      adjustmentModalDateString: dateString
    })
  },

  /**
   * 收入调整成功
   */
  onIncomeAdjustmentSuccess(e) {
    const { mode, type, amount, description, id, isEdit, editItem } = e.detail

    // 如果是编辑模式，需要调用编辑服务
    if (isEdit && editItem) {
      this.handleEditIncomeAdjustment(mode, type, amount, description, editItem)
      return
    }

    // 显示成功提示
    const modeText = mode === 'income' ? '额外收入' : '扣款'
    wx.showToast({
      title: `${modeText}添加成功`,
      icon: 'success',
      duration: 2000
    })

    // 重新加载选中日期的数据
    this.loadSelectedDateData()

    // 更新统计信息
    this.updateStatistics()

    // 触发全局数据更新事件
    this.triggerDataUpdateEvent()
  },

  /**
   * 处理编辑收入调整
   */
  async handleEditIncomeAdjustment(mode, type, amount, description, editItem) {
    try {
      const selectedDate = this.data.selectedDate

      console.log('处理编辑收入调整:', {
        mode,
        type,
        amount,
        description,
        selectedDate,
        editItem
      })

      if (!selectedDate || !editItem) {
        throw new Error('编辑数据不完整')
      }

      const incomeAdjustmentService = require('../../core/services/income-adjustment-service.js')

      let result
      if (mode === 'income') {
        result = incomeAdjustmentService.editExtraIncome(
          selectedDate,
          editItem,
          type,
          amount,
          description
        )
      } else {
        result = incomeAdjustmentService.editDeduction(
          selectedDate,
          editItem,
          type,
          amount,
          description
        )
      }

      console.log('编辑结果:', result)

      // 显示成功提示
      const modeText = mode === 'income' ? '额外收入' : '扣款'
      wx.showToast({
        title: `${modeText}编辑成功`,
        icon: 'success',
        duration: 2000
      })

      // 重新加载选中日期的数据
      this.loadSelectedDateData()

      // 更新统计信息
      this.updateStatistics()

      // 触发全局数据更新事件
      this.triggerDataUpdateEvent()

    } catch (error) {
      console.error('编辑收入调整失败:', error)
      wx.showToast({
        title: '编辑失败: ' + error.message,
        icon: 'error',
        duration: 3000
      })
    }
  },

  /**
   * 收入调整错误
   */
  onIncomeAdjustmentError(e) {
    const { message } = e.detail
    console.error('收入调整添加失败:', message)

    wx.showToast({
      title: message || '操作失败',
      icon: 'error',
      duration: 2000
    })
  },

  /**
   * 关闭收入调整模态框
   */
  onIncomeAdjustmentModalClose() {
    this.setData({
      showIncomeAdjustmentModal: false,
      adjustmentModalIsEdit: false,
      adjustmentModalEditItem: null,
      adjustmentModalDateString: ''
    })
  },

  /**
   * 删除额外收入
   */
  onDeleteExtraIncome(e) {
    const itemId = e.currentTarget.dataset.id
    console.log('删除额外收入, ID:', itemId)

    wx.showModal({
      title: '确认删除',
      content: '确定要删除这条额外收入记录吗？',
      success: (res) => {
        if (res.confirm) {
          try {
            incomeAdjustmentService.removeExtraIncome(
              this.data.selectedDate,
              itemId,
              this.data.currentWorkId
            )

            wx.showToast({
              title: '删除成功',
              icon: 'success'
            })

            // 重新加载数据
            this.loadSelectedDateData()
            this.updateStatistics()
            this.triggerDataUpdateEvent()

          } catch (error) {
            console.error('删除额外收入失败:', error)
            wx.showToast({
              title: '删除失败',
              icon: 'error'
            })
          }
        }
      }
    })
  },

  /**
   * 删除扣款
   */
  onDeleteDeduction(e) {
    const itemId = e.currentTarget.dataset.id
    console.log('删除扣款, ID:', itemId)

    wx.showModal({
      title: '确认删除',
      content: '确定要删除这条扣款记录吗？',
      success: (res) => {
        if (res.confirm) {
          try {
            incomeAdjustmentService.removeDeduction(
              this.data.selectedDate,
              itemId,
              this.data.currentWorkId
            )

            wx.showToast({
              title: '删除成功',
              icon: 'success'
            })

            // 重新加载数据
            this.loadSelectedDateData()
            this.updateStatistics()
            this.triggerDataUpdateEvent()

          } catch (error) {
            console.error('删除扣款失败:', error)
            wx.showToast({
              title: '删除失败',
              icon: 'error'
            })
          }
        }
      }
    })
  }
})