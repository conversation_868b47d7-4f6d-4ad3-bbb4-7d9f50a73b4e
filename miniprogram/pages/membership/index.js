// 会员页面
import { api } from '../../core/api/index.js'
import { formatRefreshTime } from '../../utils/time-utils.js'
const { isVipMember, showVipMembershipInfo, getMembershipTypeText } = require('../../utils/membership.js')

Page({
  data: {
    // 用户信息
    userInfo: {
      no: null,
      avatar: '',
      nickname: '加载中...',
      vip: {
        status: false,
        expiredAt: null
      },
      points: 0,
      isLoggedIn: false
    },

    // VIP到期时间显示文本
    vipExpireText: '',

    // 会员统计数据
    membershipStats: {
      membershipDays: 0,
      membershipUsageDays: 0,
      apiCallsToday: 0,
      apiCallsThisMonth: 0,
      membershipLevel: {
        level: 0,
        name: '普通用户',
        icon: '👤',
        color: '#6c757d'
      },
      joinDate: '',
      totalPoints: 0
    },



    // VIP记录相关
    vipRecords: [],
    vipRecordsStats: {
      totalDays: 0,
      totalRecords: 0
    },
    showVipRecordsModal: false,

    // 兑换码模态框
    showRedeemModal: false,
    defaultRedeemCode: '',

    // 邀请好友模态框
    showInviteModal: false,
    inviteCode: '',

    // 反馈模态框
    showFeedbackModal: false,
    feedbackType: 'suggestion',
    feedbackContent: '',
    feedbackContact: '',

    // 会员信息
    membershipInfo: {
      expireDate: '',
      expireDateText: '',
      daysRemaining: 0,
      isNewUser: false,
      canWatchAd: true,
      lastAdWatchTime: null
    }
  },

  /**
   * 页面加载时
   */
  onLoad() {
    console.log('会员页面加载')
    this.initializePage()
  },

  /**
   * 初始化页面
   */
  async initializePage() {
    try {
      // 等待用户管理器初始化完成
      await this.waitForUserManager()

      // 加载页面数据
      await Promise.all([
        this.loadUserInfo(),
        this.loadMembershipStats(),
        this.loadVipRecordsStats()
      ])
    } catch (error) {
      console.error('页面初始化失败:', error)
      wx.showToast({
        title: '页面加载失败，请重试',
        icon: 'none'
      })
    }
  },

  /**
   * 等待用户管理器初始化
   */
  async waitForUserManager() {
    return new Promise((resolve, reject) => {
      const checkUserManager = () => {
        const app = getApp()
        if (app.globalData.userManager) {
          this.userManager = app.globalData.userManager
          console.log('用户管理器初始化完成')
          resolve()
        } else {
          // 继续等待
          setTimeout(checkUserManager, 100)
        }
      }

      // 开始检查
      checkUserManager()

      // 设置超时
      setTimeout(() => {
        if (!this.userManager) {
          console.error('用户管理器初始化超时')
          reject(new Error('用户管理器初始化超时'))
        }
      }, 5000)
    })
  },

  /**
   * 页面显示时
   */
  onShow() {
    console.log('会员页面显示')

    // 确保用户管理器存在
    if (!this.userManager) {
      const app = getApp()
      if (app.globalData.userManager) {
        this.userManager = app.globalData.userManager
      } else {
        console.error('用户管理器未初始化')
        return
      }
    }

    // 刷新数据
    this.loadUserInfo()
    this.loadMembershipStats()
    this.loadVipRecordsStats()
  },

  /**
   * 加载用户信息
   */
  async loadUserInfo() {
    try {
      // 先从本地获取基础信息
      const localUserInfo = this.userManager.getUserInfo()
      const isLoggedIn = this.userManager.isUserLoggedIn()

      if (!isLoggedIn) {
        this.setData({
          'userInfo.nickname': '未登录',
          'userInfo.vip': { status: false, expiredAt: null },
          'userInfo.points': 0,
          'userInfo.isLoggedIn': false
        })
        return
      }

      console.log('[Membership] 开始获取用户信息')

      // 从API获取最新用户信息
      const result = await api.user.getUserInfo()

      if (result.success) {
        const userInfo = result.data

        this.setData({
          'userInfo.no': userInfo.no || null,
          'userInfo.avatar': userInfo.avatar || '',
          'userInfo.nickname': userInfo.nickname || '未设置昵称',
          'userInfo.vip': userInfo.vip || { status: false, expiredAt: null },
          'userInfo.points': userInfo.points || 0,
          'userInfo.isLoggedIn': true
        })

        // 更新本地缓存
        this.userManager.updateUserInfo(userInfo)

        console.log('[Membership] 用户信息加载完成:', userInfo)
        console.log('[Membership] VIP信息:', userInfo.vip)
        console.log('[Membership] 设置后的用户信息:', this.data.userInfo)

        // 计算VIP到期时间显示文本
        this.updateVipExpireText()
      } else {
        console.error('[Membership] 获取用户信息失败:', result.message)
        // 使用本地缓存信息
        this.setData({
          'userInfo.no': localUserInfo ? localUserInfo.no : null,
          'userInfo.avatar': localUserInfo ? localUserInfo.avatar : '',
          'userInfo.nickname': localUserInfo ? localUserInfo.nickname : '未登录',
          'userInfo.vip': localUserInfo ? localUserInfo.vip : { status: false, expiredAt: null },
          'userInfo.points': localUserInfo ? (localUserInfo.points || 0) : 0,
          'userInfo.isLoggedIn': isLoggedIn
        })
        this.updateVipExpireText()
      }
    } catch (error) {
      console.error('加载用户信息失败:', error)
      // 使用本地缓存信息作为备用
      const localUserInfo = this.userManager.getUserInfo()
      const isLoggedIn = this.userManager.isUserLoggedIn()

      this.setData({
        'userInfo.no': localUserInfo ? localUserInfo.no : null,
        'userInfo.avatar': localUserInfo ? localUserInfo.avatar : '',
        'userInfo.nickname': localUserInfo ? localUserInfo.nickname : '未登录',
        'userInfo.vip': localUserInfo ? localUserInfo.vip : { status: false, expiredAt: null },
        'userInfo.points': localUserInfo ? (localUserInfo.points || 0) : 0,
        'userInfo.isLoggedIn': isLoggedIn
      })
      this.updateVipExpireText()
    }
  },

  /**
   * 更新VIP到期时间显示文本
   */
  updateVipExpireText() {
    const userInfo = this.data.userInfo
    console.log('更新VIP到期时间，用户信息:', userInfo)

    if (userInfo && userInfo.vip && userInfo.vip.status && userInfo.vip.expiredAt) {
      const expireDate = new Date(userInfo.vip.expiredAt)
      const now = new Date()
      const daysRemaining = Math.ceil((expireDate.getTime() - now.getTime()) / (1000 * 60 * 60 * 24))

      console.log('VIP到期日期:', expireDate, '剩余天数:', daysRemaining)

      if (daysRemaining > 0) {
        this.setData({
          vipExpireText: `还有${daysRemaining}天到期`
        })
      } else {
        this.setData({
          vipExpireText: '已过期'
        })
      }
    } else {
      console.log('用户非VIP或缺少VIP信息')
      this.setData({
        vipExpireText: ''
      })
    }
  },

  /**
   * 加载会员统计数据
   */
  async loadMembershipStats() {
    try {
      console.log('[Membership] 开始加载会员统计')

      const result = await api.user.getMembershipStats()

      if (result.success) {
        console.log('[Membership] 会员统计数据加载成功:', result.data)
        this.setData({
          membershipStats: result.data
        })
      } else {
        console.error('[Membership] 获取会员统计失败:', result.message)
      }
    } catch (error) {
      console.error('[Membership] 加载会员统计失败:', error)
    }
  },



  /**
   * 设置默认会员信息
   */
  setDefaultMembershipInfo(isVip) {
    const isNewUser = this.checkIsNewUser()

    this.setData({
      membershipInfo: {
        expireDate: isVip ? '获取中...' : '',
        expireDateText: isVip ? '获取中...' : '',
        daysRemaining: 0,
        isNewUser: isNewUser,
        canWatchAd: this.checkCanWatchAd(),
        lastAdWatchTime: null
      }
    })
  },

  /**
   * 检查是否为新用户
   */
  checkIsNewUser() {
    try {
      const userInfo = this.data.userInfo
      return !userInfo.userId || userInfo.userId === '未知'
    } catch (error) {
      console.error('检查新用户状态失败:', error)
      return false
    }
  },

  /**
   * 检查是否可以观看广告
   */
  checkCanWatchAd() {
    // 简化逻辑：每天可以观看一次广告
    const lastAdWatchTime = wx.getStorageSync('lastAdWatchTime')
    if (!lastAdWatchTime) {
      return true
    }

    const lastWatchDate = new Date(lastAdWatchTime).toDateString()
    const today = new Date().toDateString()
    
    return lastWatchDate !== today
  },

  /**
   * 格式化日期
   */
  formatDate(date) {
    const year = date.getFullYear()
    const month = String(date.getMonth() + 1).padStart(2, '0')
    const day = String(date.getDate()).padStart(2, '0')
    return `${year}-${month}-${day}`
  },

  /**
   * 加载VIP记录统计
   */
  async loadVipRecordsStats() {
    try {
      const result = await wx.cloud.callFunction({
        name: 'cloud-functions',
        data: {
          type: 'getVipRecordsStats',
          data: {}
        }
      })

      if (result.result.success) {
        this.setData({
          vipRecordsStats: result.result.data
        })
      }
    } catch (error) {
      console.error('加载VIP记录统计失败:', error)
    }
  },

  /**
   * 查看VIP记录
   */
  async onViewVipRecords() {
    try {
      wx.showLoading({ title: '加载中...' })

      console.log('[Membership] 开始加载VIP记录')

      const result = await api.user.getVipRecords({ limit: 50 })

      wx.hideLoading()

      if (result.success) {
        // 格式化记录数据
        const formattedRecords = result.data.map(record => ({
          ...record,
          dateText: this.formatDateTime(record.createdAt)
        }))

        this.setData({
          vipRecords: formattedRecords,
          showVipRecordsModal: true
        })
        console.log('[Membership] VIP记录加载成功')
      } else {
        wx.showToast({
          title: result.message || '加载失败',
          icon: 'none'
        })
      }
    } catch (error) {
      wx.hideLoading()
      console.error('[Membership] 加载VIP记录失败:', error)
      wx.showToast({
        title: error.message || '网络异常，请重试',
        icon: 'none'
      })
    }
  },

  /**
   * 格式化日期时间
   */
  formatDateTime(dateStr) {
    if (!dateStr) return ''
    
    const date = new Date(dateStr)
    const now = new Date()
    const diff = now.getTime() - date.getTime()
    const days = Math.floor(diff / (1000 * 60 * 60 * 24))

    if (days === 0) {
      return '今天'
    } else if (days === 1) {
      return '昨天'
    } else if (days < 7) {
      return `${days}天前`
    } else {
      return `${date.getMonth() + 1}月${date.getDate()}日`
    }
  },

  /**
   * 关闭VIP记录模态框
   */
  onCloseVipRecordsModal() {
    this.setData({
      showVipRecordsModal: false
    })
  },



  /**
   * 跳转到积分商店
   */
  onGoToStore() {
    wx.navigateTo({
      url: '/pages/store/index'
    })
  },

  /**
   * 显示兑换码模态框
   */
  onShowRedeemModal() {
    this.setData({
      showRedeemModal: true,
      defaultRedeemCode: ''
    })
  },

  /**
   * 关闭兑换码模态框
   */
  onCloseRedeemModal() {
    this.setData({
      showRedeemModal: false
    })
  },

  /**
   * 兑换成功回调
   */
  onRedeemSuccess(e) {
    console.log('兑换成功:', e.detail)
    // 刷新用户信息和会员信息
    this.loadUserInfo()
    this.loadMembershipInfo()
    this.loadVipRecordsStats()
  },



  /**
   * 显示升级选项
   */
  onShowUpgradeOptions() {
    wx.showActionSheet({
      itemList: ['🎫 兑换码', '👥 邀请好友'],
      success: (res) => {
        switch (res.tapIndex) {
          case 0:
            this.onShowRedeemModal()
            break
          case 1:
            this.onShowInviteModal()
            break
        }
      }
    })
  },

  /**
   * 专属功能 - 云端同步
   */
  onGoToCloudSync() {
    wx.showToast({
      title: '功能开发中',
      icon: 'none'
    })
  },

  /**
   * 专属功能 - 高级分析
   */
  onGoToAdvancedAnalytics() {
    wx.showToast({
      title: '功能开发中',
      icon: 'none'
    })
  },

  /**
   * 专属功能 - 历史数据
   */
  onGoToHistoryData() {
    wx.showToast({
      title: '功能开发中',
      icon: 'none'
    })
  },

  /**
   * 专属功能 - 数据导出
   */
  onGoToDataExport() {
    wx.showToast({
      title: '功能开发中',
      icon: 'none'
    })
  },



  /**
   * 显示邀请模态框
   */
  onShowInviteModal() {
    this.setData({
      showInviteModal: true
    })
  },

  /**
   * 关闭邀请模态框
   */
  onCloseInviteModal() {
    this.setData({
      showInviteModal: false
    })
  },

  /**
   * 生成邀请码
   */
  async onGenerateInviteCode() {
    try {
      wx.showLoading({ title: '生成中...' })

      const result = await wx.cloud.callFunction({
        name: 'cloud-functions',
        data: {
          type: 'generateInviteCode',
          data: {}
        }
      })

      wx.hideLoading()

      if (result.result.success) {
        this.setData({
          inviteCode: result.result.data.inviteCode
        })
        wx.showToast({
          title: '邀请码生成成功',
          icon: 'success'
        })
      } else {
        wx.showToast({
          title: result.result.message || '生成失败',
          icon: 'none'
        })
      }
    } catch (error) {
      wx.hideLoading()
      console.error('生成邀请码失败:', error)
      wx.showToast({
        title: '网络异常，请重试',
        icon: 'none'
      })
    }
  },

  /**
   * 复制邀请码
   */
  onCopyInviteCode() {
    wx.setClipboardData({
      data: this.data.inviteCode,
      success: () => {
        wx.showToast({
          title: '邀请码已复制',
          icon: 'success'
        })
      }
    })
  },

  /**
   * 分享邀请码
   */
  onShareInviteCode() {
    wx.showToast({
      title: '功能开发中',
      icon: 'none'
    })
  },

  /**
   * 显示反馈模态框
   */
  onShowFeedbackModal() {
    this.setData({
      showFeedbackModal: true,
      feedbackType: 'suggestion',
      feedbackContent: '',
      feedbackContact: ''
    })
  },

  /**
   * 关闭反馈模态框
   */
  onCloseFeedbackModal() {
    this.setData({
      showFeedbackModal: false
    })
  },

  /**
   * 选择反馈类型
   */
  onSelectFeedbackType(e) {
    const type = e.currentTarget.dataset.type
    this.setData({
      feedbackType: type
    })
  },

  /**
   * 反馈内容输入
   */
  onFeedbackContentChange(e) {
    this.setData({
      feedbackContent: e.detail.value
    })
  },

  /**
   * 反馈联系方式输入
   */
  onFeedbackContactChange(e) {
    this.setData({
      feedbackContact: e.detail.value
    })
  },

  /**
   * 提交反馈
   */
  async onSubmitFeedback() {
    const { feedbackType, feedbackContent, feedbackContact } = this.data

    if (!feedbackContent.trim()) {
      wx.showToast({
        title: '请输入反馈内容',
        icon: 'none'
      })
      return
    }

    try {
      wx.showLoading({ title: '提交中...' })

      const result = await wx.cloud.callFunction({
        name: 'cloud-functions',
        data: {
          type: 'submitMembershipFeedback',
          data: {
            type: feedbackType,
            content: feedbackContent,
            contact: feedbackContact
          }
        }
      })

      wx.hideLoading()

      if (result.result.success) {
        wx.showToast({
          title: '反馈提交成功',
          icon: 'success'
        })
        this.onCloseFeedbackModal()
      } else {
        wx.showToast({
          title: result.result.message || '提交失败',
          icon: 'none'
        })
      }
    } catch (error) {
      wx.hideLoading()
      console.error('提交反馈失败:', error)
      wx.showToast({
        title: '网络异常，请重试',
        icon: 'none'
      })
    }
  },

  /**
   * 下拉刷新
   */
  onPullDownRefresh() {
    console.log('下拉刷新会员页面')
    Promise.all([
      this.loadUserInfo(),
      this.loadMembershipStats(),
      this.loadVipRecordsStats()
    ]).finally(() => {
      wx.stopPullDownRefresh()
    })
  },

  /**
   * 阻止事件冒泡
   */
  stopPropagation() {
    // 阻止事件冒泡
  }
})
