<!-- 简化的测试WXML -->

<view class="page">
  <view class="header">
    <text class="title">批量操作组件测试</text>
  </view>
  
  <view class="content">
    <button class="test-btn" bind:tap="onBatchCopy">
      打开批量复制模态框
    </button>
    
    <view class="debug-info">
      <text class="debug-title">调试信息：</text>
      <text class="debug-item">showBatchModal: {{showBatchModal}}</text>
      <text class="debug-item">batchOperation: {{batchOperation}}</text>
      <text class="debug-item">currentWorkId: {{currentWorkId}}</text>
    </view>
  </view>

  <!-- 批量操作模态框组件 -->
  <batch-operation-modal
    visible="{{showBatchModal}}"
    operation="{{batchOperation}}"
    timeSegmentService="{{timeSegmentService}}"
    holidayManager="{{holidayManager}}"
    currentWorkId="{{currentWorkId}}"
    currentDate="{{currentDate}}"
    employmentStartDate="{{employmentStartDate}}"
    employmentEndDate="{{employmentEndDate}}"
    bind:close="onCloseBatchModal"
    bind:cancel="onCancelBatchOperation"
    bind:dataUpdated="onBatchDataUpdated"
  />
</view>
