/**
 * 管理员权限测试
 * 用于测试管理员权限控制功能
 */

const { checkAdminPermission, validateAdminPermission } = require('../utils/auth')
const usersDB = require('../db/users')

/**
 * 测试管理员权限检查功能
 */
async function testAdminPermission() {
  console.log('=== 管理员权限测试开始 ===')

  try {
    // 测试1: 检查不存在的用户
    console.log('\n测试1: 检查不存在的用户')
    const result1 = await checkAdminPermission('non-existent-openid')
    console.log('结果:', result1) // 应该返回 false

    // 测试2: 检查普通用户
    console.log('\n测试2: 检查普通用户权限')
    // 这里需要替换为实际的普通用户openid
    const normalUserOpenid = 'test-normal-user-openid'
    const result2 = await checkAdminPermission(normalUserOpenid)
    console.log('结果:', result2) // 应该返回 false

    // 测试3: 验证管理员权限
    console.log('\n测试3: 验证管理员权限')
    const validation1 = await validateAdminPermission('non-existent-openid')
    console.log('验证结果:', validation1)
    // 应该返回 { success: false, message: '操作失败', needAdmin: true }

    console.log('\n=== 管理员权限测试完成 ===')
    
    return {
      success: true,
      message: '管理员权限测试完成'
    }
  } catch (error) {
    console.error('测试失败:', error)
    return {
      success: false,
      message: error.message || '测试失败'
    }
  }
}

/**
 * 创建测试管理员用户
 * 注意：这个函数仅用于测试，生产环境中应该手动在数据库中设置
 */
async function createTestAdminUser(openid) {
  try {
    console.log(`创建测试管理员用户: ${openid}`)
    
    // 检查用户是否已存在
    const existingUser = await usersDB.findByOpenid(openid)
    if (existingUser.success && existingUser.data) {
      // 用户已存在，更新为管理员
      const updateResult = await usersDB.updateByOpenid(openid, { isAdmin: true })
      console.log('更新用户为管理员:', updateResult)
      return updateResult
    } else {
      // 创建新的管理员用户
      const createResult = await usersDB.createUser({
        openid: openid,
        nickname: '测试管理员',
        avatar: '',
        isAdmin: true
      })
      console.log('创建管理员用户:', createResult)
      return createResult
    }
  } catch (error) {
    console.error('创建测试管理员用户失败:', error)
    return {
      success: false,
      message: error.message || '创建失败'
    }
  }
}

/**
 * 测试需要管理员权限的接口
 */
async function testAdminOnlyAPIs() {
  console.log('\n=== 测试管理员专用接口 ===')
  
  const adminAPIs = [
    'initializeStoreItems',
    'createChangelogCollection', 
    'cleanupExpiredFishingStatus',
    'createFriendApp',
    'updateFriendApp',
    'deleteFriendApp',
    'toggleFriendAppVisibility',
    'getFriendAppStats'
  ]

  console.log('需要管理员权限的接口列表:')
  adminAPIs.forEach((api, index) => {
    console.log(`${index + 1}. ${api}`)
  })

  console.log('\n注意：这些接口现在都已添加管理员权限验证')
  console.log('普通用户调用时会返回"操作失败"错误，不会暴露权限不足信息')
  
  return {
    success: true,
    adminAPIs: adminAPIs
  }
}

module.exports = {
  testAdminPermission,
  createTestAdminUser,
  testAdminOnlyAPIs
}

// 如果直接运行此文件，执行测试
if (require.main === module) {
  (async () => {
    await testAdminPermission()
    await testAdminOnlyAPIs()
  })()
}
