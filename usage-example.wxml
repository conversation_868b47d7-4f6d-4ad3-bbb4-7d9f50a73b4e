<!-- 页面中使用批量操作模态框组件的WXML示例 -->

<view class="page-container">
  <!-- 页面主要内容 -->
  <view class="main-content">
    <!-- 操作按钮 -->
    <view class="action-buttons">
      <view class="action-btn primary" bind:tap="onBatchCopy">
        <text class="btn-icon">📋</text>
        <text class="btn-text">批量复制</text>
      </view>
      
      <view class="action-btn secondary" bind:tap="onImportSchedule">
        <text class="btn-icon">📥</text>
        <text class="btn-text">导入安排</text>
      </view>
    </view>
    
    <!-- 其他页面内容 -->
    <view class="content-area">
      <!-- 这里是你的页面主要内容 -->
      <text>页面主要内容区域</text>
    </view>
  </view>

  <!-- 批量操作模态框组件 -->
  <batch-operation-modal
    visible="{{showBatchModal}}"
    operation="{{batchOperation}}"
    timeSegmentService="{{timeSegmentService}}"
    holidayManager="{{holidayManager}}"
    currentWorkId="{{currentWorkId}}"
    currentDate="{{currentDate}}"
    employmentStartDate="{{employmentStartDate}}"
    employmentEndDate="{{employmentEndDate}}"
    bind:close="onCloseBatchModal"
    bind:cancel="onCancelBatchOperation"
    bind:dataUpdated="onBatchDataUpdated"
  />
</view>

<!-- 样式示例 -->
<style>
.page-container {
  min-height: 100vh;
  background-color: #f5f5f5;
  padding: 20px;
}

.main-content {
  background: white;
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.action-buttons {
  display: flex;
  gap: 12px;
  margin-bottom: 20px;
}

.action-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 16px;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
  border: none;
  font-size: 14px;
  font-weight: 500;
}

.action-btn.primary {
  background-color: #007aff;
  color: white;
}

.action-btn.primary:hover {
  background-color: #0056b3;
}

.action-btn.secondary {
  background-color: #f8f9fa;
  color: #333;
  border: 1px solid #e9ecef;
}

.action-btn.secondary:hover {
  background-color: #e9ecef;
}

.btn-icon {
  font-size: 16px;
}

.btn-text {
  font-size: 14px;
}

.content-area {
  padding: 20px;
  background-color: #f8f9fa;
  border-radius: 8px;
  text-align: center;
  color: #666;
}
</style>
